<?php
/**
 * Webkul_Grid Add New Row Form Admin Block.
 * @category    Webkul
 * @package     Webkul_Grid
 * <AUTHOR> Software Private Limited
 *
 */
namespace Coditron\Lixtask\Block\Adminhtml\Grid\Edit;

/**
 * Adminhtml Add New Row Form.
 */
class Form extends \Magento\Backend\Block\Widget\Form\Generic
{
    /**
     * @var \Magento\Store\Model\System\Store
     */
    protected $_systemStore;

    /**
     * @param \Magento\Backend\Block\Template\Context $context,
     * @param \Magento\Framework\Registry $registry,
     * @param \Magento\Framework\Data\FormFactory $formFactory,
     * @param \Magento\Cms\Model\Wysiwyg\Config $wysiwygConfig,
     * @param \Coditron\Lixtask\Model\Status $options,
     */
    public function __construct(
        \Magento\Backend\Block\Template\Context $context,
        \Magento\Framework\Registry $registry,
        \Magento\Framework\Data\FormFactory $formFactory,
        \Magento\Cms\Model\Wysiwyg\Config $wysiwygConfig,
        \Coditron\Lixtask\Model\Status $options,
        array $data = []
    ) {
        $this->_options = $options;
        $this->_wysiwygConfig = $wysiwygConfig;
        parent::__construct($context, $registry, $formFactory, $data);
    }

    /**
     * Prepare form.
     *
     * @return $this
     */
    protected function _prepareForm()
    {
        $dateFormat = $this->_localeDate->getDateFormat(\IntlDateFormatter::SHORT);
        $model = $this->_coreRegistry->registry('row_data');
        $form = $this->_formFactory->create(
            ['data' => [
                            'id' => 'edit_form',
                            'enctype' => 'multipart/form-data',
                            'action' => $this->getData('action'),
                            'method' => 'post'
                        ]
            ]
        );

        $form->setHtmlIdPrefix('wkgrid_');
        if ($model->getEntityId()) {
            $fieldset = $form->addFieldset(
                'base_fieldset',
                ['legend' => __('Edit Reward Data'), 'class' => 'fieldset-wide']
            );
            $fieldset->addField('entity_id', 'hidden', ['name' => 'entity_id']);
        } else {
            $fieldset = $form->addFieldset(
                'base_fieldset',
                ['legend' => __('Add Reward Data'), 'class' => 'fieldset-wide']
            );
        }

        $fieldset->addField(
            'task_id',
            'text',
            [
                'name' => 'task_id',
                'label' => __('Task_Id'),
                'id' => 'task_id',
                'title' => __('ID of task to create reward for.'),
                'class' => 'required-entry',
                'required' => true,
            ]
        );

        $wysiwygConfig = $this->_wysiwygConfig->getConfig(['tab_id' => $this->getTabId()]);

        $fieldset->addField(
            'item_cost',
            'text',
            [
                'name' => 'item_cost',
                'label' => __('Item_Cost'),
                'id' => 'item_cost',
                'title' => __('When a customer spends up to this amount, they will be rewarded with the percentage or ratio value in the item reward field?'),
                'class' => 'required-entry',
                'required' => true,
            ]
        );

        $fieldset->addField(
            'item_currency',
            'text',
            [
                'name' => 'item_currency',
                'label' => __('Item_Currency'),
                'id' => 'item_currency',
                'title' => __('Enter the local currency customer will spend to earn reward. currency code i.e USD, GBP, EUR'),
                'class' => 'required-entry',
                'required' => true,
            ]
        );
        $fieldset->addField(
            'item_reward',
            'text',
            [
                'name' => 'item_reward',
                'label' => __('Item_Reward'),
                'id' => 'item_reward',
                'title' => __('How much LUSD should the member be rewarded with? percentage or ratio value'),
                'class' => 'required-entry',
                'required' => true,
            ]
        );
        $fieldset->addField(
            'is_active',
            'select',
            [
                'name' => 'is_active',
                'label' => __('Status'),
                'id' => 'is_active',
                'title' => __('Status'),
                'values' => $this->_options->getOptionArray(),
                'class' => 'status',
                'required' => true,
            ]
        );
        $form->setValues($model->getData());
        $form->setUseContainer(true);
        $this->setForm($form);

        return parent::_prepareForm();
    }
}
