<?php
/**
 * Webkul_Grid Add New Row Form Admin Block.
 * @category    Webkul
 * @package     Webkul_Grid
 * <AUTHOR> Software Private Limited
 *
 */
namespace Coditron\Lixtask\Block\Adminhtml\Scrachcard\Edit;

use Coditron\Lixtask\Helper\Api;
/**
 * Adminhtml Add New Row Form.
 */
class Form extends \Magento\Backend\Block\Widget\Form\Generic
{
    /**
     * @var \Magento\Store\Model\System\Store
     */
    protected $_systemStore;

       /**
     * @var Api
    */
    protected $apiHelper;

    /**
     * @param \Magento\Backend\Block\Template\Context $context,
     * @param \Magento\Framework\Registry $registry,
     * @param \Magento\Framework\Data\FormFactory $formFactory,
     * @param \Magento\Cms\Model\Wysiwyg\Config $wysiwygConfig,
     * @param \Coditron\Lixtask\Model\Status $options,
     * @param Api $apiHelper
     */
    public function __construct(
        \Magento\Backend\Block\Template\Context $context,
        \Magento\Framework\Registry $registry,
        \Magento\Framework\Data\FormFactory $formFactory,
        \Magento\Cms\Model\Wysiwyg\Config $wysiwygConfig,
        \Coditron\Lixtask\Model\Status $options,
        Api $apiHelper,
        array $data = []
    ) {
        $this->_options = $options;
        $this->_wysiwygConfig = $wysiwygConfig;
        $this->apiHelper = $apiHelper;
        parent::__construct($context, $registry, $formFactory, $data);
    }

    /**
     * Prepare form.
     *
     * @return $this
     */
    protected function _prepareForm()
    {
        $dateFormat = $this->_localeDate->getDateFormat(\IntlDateFormatter::SHORT);
        $model = $this->_coreRegistry->registry('scrachcard_data');
        $form = $this->_formFactory->create(
            ['data' => [
                            'id' => 'edit_form',
                            'enctype' => 'multipart/form-data',
                            'action' => $this->getData('action'),
                            'method' => 'post'
                        ]
            ]
        );

        $form->setHtmlIdPrefix('scgrid_');
        if ($model->getId()) {
            $fieldset = $form->addFieldset(
                'base_fieldset',
                ['legend' => __('Edit Scratch Card'), 'class' => 'fieldset-wide']
            );
            $fieldset->addField('id', 'hidden', ['name' => 'id']);
        } else {
            $fieldset = $form->addFieldset(
                'base_fieldset',
                ['legend' => __('Add Scratch Card'), 'class' => 'fieldset-wide']
            );
        }

        $fieldset->addField(
            'task_id',
            'hidden',
            [
                'name' => 'task_id',
                'label' => __('Task ID'),
                'id' => 'task_id',
                'title' => __('Task ID'),
                'value' => $model->getTaskId(), // Assuming the model has a method to get the task ID
            ]
        );

        $fieldset->addField(
            'project_id',
            'text',
            [
                'name' => 'project_id',
                'label' => __('Project Id'),
                'id' => 'project_id',
                'title' => __('Project Id.'),
                'class' => 'validate-digits',
               // 'class' => 'required-entry',
               // 'required' => true,
            ]
        );

        $fieldset->addField(
            'title',
            'text',
            [
                'name' => 'title',
                'label' => __('Title'),
                'id' => 'title',
                'title' => __('Title'),
               // 'class' => 'required-entry',
               // 'required' => true,
            ]
        );

        $currencyOptions = $this->apiHelper->getWalletName();

     /*   $fieldset->addField(
            'custom_currency_organisation_id',
            'select',
            [
                'name' => 'custom_currency_organisation_id',
                'label' => __('Custom Currency Organisation Id'),
                'id' => 'custom_currency_organisation_id',
                'title' => __('Custom Currency Organisation Id'),
                // 'class' => 'required-entry',
                // 'required' => true,
                'values' => [
                    ['value' => 1724, 'label' => __('LIXX')],
                    ['value' => 2733, 'label' => __('LIXCA')],
                    // Add more options as needed
                ],
            ]
        );
        */

        $fieldset->addField(
            'custom_currency_organisation_id',
            'select',
            [
                'name' => 'custom_currency_organisation_id',
                'label' => __('Custom Currency Organisation Id'),
                'id' => 'custom_currency_organisation_id',
                'title' => __('Custom Currency Organisation Id'),
                'values' => $currencyOptions,
            ]
        );
        
        $fieldset->addField(
            'total_coins_available',
            'text',
            [
                'name' => 'total_coins_available',
                'label' => __('Total coins Available'),
                'id' => 'total_coins_available',
                'title' => __('Total coins Available'),
                'class' => 'validate-digits',
               // 'class' => 'required-entry',
               // 'required' => true,
            ]
        );

        $fieldset->addField(
            'reward_amounts',
            'text',
            [
                'name' => 'reward_amounts',
                'label' => __('Reward Amounts'),
                'id' => 'reward_amounts',
                'title' => __('Reward Amounts. To add multiple reward amounts, separate them by comma. e.g. 2, 3'),
                // 'class' => 'required-entry',
                // 'required' => true,
            ]
        );

       
        $fieldset->addField(
            'approval_type',
            'text',
            [
                'name' => 'approval_type',
                'label' => __('Approval Type'),
                'id' => 'approval_type',
                'title' => __('Approval Type'),
                'value' => 'automatic', // Set the value to be saved
                'readonly' => 'true'
            ]
        );

        $fieldset->addField(
            'reward_expires_in',
            'text',
            [
                'name' => 'reward_expires_in',
                'label' => __('Reward Expires In'),
                'id' => 'reward_expires_in',
                'title' => __('Reward Expires In'),
                'class' => 'validate-digits',
               // 'class' => 'required-entry',
               // 'required' => true,
            ]
        );

        $fieldset->addField(
            'location_id',
            'text',
            [
                'name' => 'location_id',
                'label' => __('Location Id'),
                'id' => 'location_id',
                'title' => __('Location Id'),
                'class' => 'validate-digits',
               // 'class' => 'required-entry',
               // 'required' => true,
            ]
        );

        $fieldset->addField(
            'time_zone',
            'text',
            [
                'name' => 'time_zone',
                'label' => __('Time Zone'),
                'id' => 'time_zone',
                'title' => __('Time Zone'),
               // 'class' => 'required-entry',
               // 'required' => true,
            ]
        );

        $wysiwygConfig = $this->_wysiwygConfig->getConfig(['tab_id' => $this->getTabId()]);

        $fieldset->addField(
            'has_location_trigger',
            'select',
            [
                'name' => 'has_location_trigger',
                'label' => __('Has Location Trigger'),
                'id' => 'has_location_trigger',
                'title' => __('Has Location Trigger'),
                'class' => 'status',
                'values' => [
                    ['value' => 'true', 'label' => __('true')],
                    ['value' => 'false', 'label' => __('false')]
                ],
                'required' => true,
            ]
        );

        $fieldset->addField(
            'schedule_local_time',
            'date',
            [
                'name' => 'schedule_local_time',
                'label' => __('Schedule Local Time'),
                'id' => 'schedule_local_time',
                'title' => __('Schedule Local Time'),
                'class' => 'required-entry',
                'required' => true,
                'date_format' => 'yyyy-MM-dd',
                'time_format' => 'HH:mm:ss',
            ]
        );

        $fieldset->addField(
            'status',
            'select',
            [
                'name' => 'status',
                'label' => __('Status'),
                'id' => 'status',
                'title' => __('Status'),
                'values' => $this->_options->getOptionArray(),
                'class' => 'status',
            ]
        );

        $model->setData('approval_type', 'automatic');
        $form->setValues($model->getData());

        $form->setUseContainer(true);
        $this->setForm($form);

        return parent::_prepareForm();
    }
}
