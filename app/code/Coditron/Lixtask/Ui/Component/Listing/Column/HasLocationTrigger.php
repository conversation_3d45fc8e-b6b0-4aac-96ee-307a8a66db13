<?php
namespace Coditron\Lixtask\Ui\Component\Listing\Column;

class HasLocationTrigger extends \Magento\Ui\Component\Listing\Columns\Column
{
    public function prepareDataSource(array $dataSource)
    {
        if (isset($dataSource['data']['items'])) {
            foreach ($dataSource['data']['items'] as &$item) {
                // Convert 0/1 to true/false
                $item['has_location_trigger'] = $item['has_location_trigger'] ? 'true' : 'false';
            }
        }
        return $dataSource;
    }
}