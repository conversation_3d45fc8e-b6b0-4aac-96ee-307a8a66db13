<?php
declare(strict_types=1);

namespace Coditron\Lixtask\Api\Data;

interface CustomformInterface extends \Magento\Framework\Api\ExtensibleDataInterface
{
    const string ID = 'id';
    const string MESSAGE = 'message';
    const string FIRST_NAME = 'first_name';
    const string LAST_NAME = 'last_name';
    const string CREATED_AT = 'created_at';
    const string EMAIL = 'email';
    const string PHONE = 'phone';
    const string IMAGE = 'image';
    const string STATUS = 'status';
    const string TASK_ID = 'task_id';
    const string TITLE = 'title';
    const string TITLE_SHOW = 'task_title_show';
    const string TASK_URL = 'task_url';
    const string DESCRIPTION = 'description';
    const string COINS_PER_ACTION = 'coins_per_action';
    const string XP_POINTS = 'xp_points';
    const string REWARD_TYPE = 'reward_type';
    const string PROOF_TYPE = 'proof_type';
    const string CUSTOM_CURRENCY_ORG_ID = 'custom_currency_organisation_id';
    const string IS_STAFF_APPROVAL = 'is_staff_approval';
    const string NUMBERR_SUBMISSIONS_ALLOWED = 'number_of_submissions_allowed';
    const string PERIOD_ALLOWED_SUBMISSIONS = 'period_allowed_for_submissions';
    const string LABEL = 'task_button_label';
    const string REWARD_EXPIRATION_IN = 'reward_expires_in';

    public function getRewardExpiresIn(): ?int;

    public function setRewardExpiresIn($expirationIn): self;

    public function getTaskButtonLabel(): ?string;

    public function setTaskButtonLabel(?string $label): self;

    public function getPeriodAllowedForSubmissions(): ?string;

    public function setPeriodAllowedForSubmissions(?string $value): self;

    public function getNumberOfSubmissionsAllowed();

    public function setNumberOfSubmissionsAllowed($numberOfSubmissionsAllowed);

    public function getIsStaffApproval();

    public function setIsStaffApproval($isStaffApproval);

    public function getCustomCurrencyOrganisationId();

    public function setCustomCurrencyOrganisationId($customCurrencyOrganisationId);

    public function getProofType();

    public function setProofType($type);

    public function getRewardType();

    public function setRewardType($rewardType);

    public function getCoinsPerAction();

    public function setCoinsPerAction($action);

    public function getXpPoints();

    public function setXpPoints($points);

    public function getDescription();

    public function setDescription($description);

    public function getTaskUrl();

    public function setTaskUrl($url);

    public function getTitleShow(): string;

    public function setTaskTitleShow(?string $titleShow): self;

    public function getTitle();

    public function setTitle(string $title);

    /**
     * Get id
     * @return string|null
     */
    public function getId();

    /**
     * Set task_id
     * @param string $taskId
     * @return \Coditron\Lixtask\Api\Data\CustomformInterface
     */
    public function setTaskId($taskId);

    /**
     * Get id
     * @return string|null
     */
    public function getTaskId();

    /**
     * Set id
     * @param string $id
     * @return \Coditron\Lixtask\Api\Data\CustomformInterface
     */
    public function setId($id);

    /**
     * Retrieve existing extension attributes object or create a new one.
     * @return \Coditron\Lixtask\Api\Data\CustomformExtensionInterface|null
     */
    public function getExtensionAttributes();

    /**
     * Set an extension attributes object.
     * @param \Coditron\Lixtask\Api\Data\CustomformExtensionInterface $extensionAttributes
     * @return $this
     */
    public function setExtensionAttributes(
        \Coditron\Lixtask\Api\Data\CustomformExtensionInterface $extensionAttributes
    );

    /**
     * Get first_name
     * @return string|null
     */
    public function getFirstName();

    /**
     * Set first_name
     * @param string $firstName
     * @return \Coditron\Lixtask\Api\Data\CustomformInterface
     */
    public function setFirstName($firstName);

	/**
     * Get last_name
     * @return string|null
     */
    public function getLastName();

    /**
     * Set last_name
     * @param string $lastName
     * @return \Coditron\Lixtask\Api\Data\CustomformInterface
     */
    public function setLastName($lastName);

    /**
     * Get email
     * @return string|null
     */
    public function getEmail();

    /**
     * Set email
     * @param string $email
     * @return \Coditron\Lixtask\Api\Data\CustomformInterface
     */
    public function setEmail($email);

	/**
     * Get phone
     * @return string|null
     */
    public function getPhone();

    /**
     * Set phone
     * @param string $phone
     * @return \Coditron\Lixtask\Api\Data\CustomformInterface
     */
    public function setPhone($phone);

    /**
     * Get message
     * @return string|null
     */
    public function getMessage();

    /**
     * Set message
     * @param string $message
     * @return \Coditron\Lixtask\Api\Data\CustomformInterface
     */
    public function setMessage($message);

    /**
     * Get image
     * @return string|null
     */
    public function getImage();

    /**
     * Set image
     * @param string $image
     * @return \Coditron\Lixtask\Api\Data\CustomformInterface
     */
    public function setImage($image);

    /**
     * Get status
     * @return string|null
     */
    public function getStatus();

    /**
     * Set status
     * @param string $status
     * @return \Coditron\Lixtask\Api\Data\CustomformInterface
     */
    public function setStatus($status);

    /**
     * Get created_at
     * @return string|null
     */
    public function getCreatedAt();

    /**
     * Set created_at
     * @param string $createdAt
     * @return \Coditron\Lixtask\Api\Data\CustomformInterface
     */
    public function setCreatedAt($createdAt);
}

