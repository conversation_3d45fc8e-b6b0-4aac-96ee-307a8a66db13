<?php
declare(strict_types=1);

namespace Coditron\Lixtask\Api\Data;

interface CustomformSearchResultsInterface extends \Magento\Framework\Api\SearchResultsInterface
{

    /**
     * Get Customform list.
     * @return \Coditron\Lixtask\Api\Data\CustomformInterface[]
     */
    public function getItems();

    /**
     * Set id list.
     * @param \Coditron\Lixtask\Api\Data\CustomformInterface[] $items
     * @return $this
     */
    public function setItems(array $items);
}

