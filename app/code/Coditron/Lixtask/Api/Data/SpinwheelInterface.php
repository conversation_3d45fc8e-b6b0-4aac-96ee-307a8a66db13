<?php
/**
 * Languagepack TranslationInterface.
 * @category  Coditron
 * @package   Coditron_Languagepack
 * <AUTHOR>
 * @copyright Copyright (c) 2024 Coditron (https://www.coditron.com/)
 * @license    https://www.coditron.com/LICENSE.txt
 */

 namespace Coditron\Lixtask\Api\Data;

interface SpinwheelInterface
{
    /**
     * Constants for keys of data array. Identical to the name of the getter in snake case.
     */

    const ID = 'id';
    const PROJECT_ID = 'project_id';
    const TITLE = 'title';
    const CUSTOM_CURRENCY_ORGANISATION_ID = 'custom_currency_organisation_id';
    const TOTAL_COINS_AVAILABLE = 'total_coins_available';
    const APPROVAL_TYPE = 'approval_type';
    const REWARD_AMOUNTS = 'reward_amounts';
    const REWARD_EXPIRES_IN = 'reward_expires_in';
    const HAS_LOCATION_TRIGGER = 'has_location_trigger';
    const LOCATION_ID = 'location_id';
    const SCHEDULE_LOCAL_TIME = 'schedule_local_time';
    const TIME_ZONE = 'time_zone';
    const STATUS = 'status';
    const TASK_ID = 'task_id';



    /**
     * Get ID
     *
     * @return int|null
     */
    public function getId();

    /**
     * Set ID
     *
     * @param int $id
     * @return \Coditron\Lixtask\Api\Data\SpinwheelInterface
     */
    public function setId($id);

    /**
     * Get Project ID
     *
     * @return int|null
     */
    public function getProjectId();

    /**
     * Set Project ID
     *
     * @param int $projectId
     * @return \Coditron\Lixtask\Api\Data\SpinwheelInterface
     */
    public function setProjectId($projectId);

    /**
     * Get Project ID
     *
     * @return int|null
     */
    public function getTaskId();

    /**
     * Set Project ID
     *
     * @param int $projectId
     * @return \Coditron\Lixtask\Api\Data\SpinwheelInterface
     */
    public function setTaskId($task_id);

    /**
     * Get Title
     *
     * @return string|null
     */
    public function getTitle();

    /**
     * Set Title
     *
     * @param string $title
     * @return \Coditron\Lixtask\Api\Data\SpinwheelInterface
     */
    public function setTitle($title);

    /**
     * Get Custom Currency Organisation ID
     *
     * @return int|null
     */
    public function getCustomCurrencyOrganisationId();

    /**
     * Set Custom Currency Organisation ID
     *
     * @param int $customCurrencyOrganisationId
     * @return \Coditron\Lixtask\Api\Data\SpinwheelInterface
     */
    public function setCustomCurrencyOrganisationId($customCurrencyOrganisationId);

    /**
     * Get Total Coins Available
     *
     * @return int|null
     */
    public function getTotalCoinsAvailable();

    /**
     * Set Total Coins Available
     *
     * @param int $totalCoinsAvailable
     * @return \Coditron\Lixtask\Api\Data\SpinwheelInterface
     */
    public function setTotalCoinsAvailable($totalCoinsAvailable);

    /**
     * Get Approval Type
     *
     * @return string|null
     */
    public function getApprovalType();

    /**
     * Set Approval Type
     *
     * @param string $approvalType
     * @return \Coditron\Lixtask\Api\Data\SpinwheelInterface
     */
    public function setApprovalType($approvalType);

    /**
     * Get Reward Amounts
     *
     * @return string|null
     */
    public function getRewardAmounts();

    /**
     * Set Reward Amounts
     *
     * @param string $rewardAmounts
     * @return \Coditron\Lixtask\Api\Data\SpinwheelInterface
     */
    public function setRewardAmounts($rewardAmounts);

    /**
     * Get Reward Expires In
     *
     * @return string|null
     */
    public function getRewardExpiresIn();

    /**
     * Set Reward Expires In
     *
     * @param string $rewardExpiresIn
     * @return \Coditron\Lixtask\Api\Data\SpinwheelInterface
     */
    public function setRewardExpiresIn($rewardExpiresIn);

    /**
     * Get Has Location Trigger
     *
     * @return bool|null
     */
    public function getHasLocationTrigger();

    /**
     * Set Has Location Trigger
     *
     * @param bool $hasLocationTrigger
     * @return \Coditron\Lixtask\Api\Data\SpinwheelInterface
     */
    public function setHasLocationTrigger($hasLocationTrigger);

    /**
     * Get Location ID
     *
     * @return int|null
     */
    public function getLocationId();

    /**
     * Set Location ID
     *
     * @param int $locationId
     * @return \Coditron\Lixtask\Api\Data\SpinwheelInterface
     */
    public function setLocationId($locationId);

    /**
     * Get Schedule Local Time
     *
     * @return string|null
     */
    public function getScheduleLocalTime();

    /**
     * Set Schedule Local Time
     *
     * @param string $scheduleLocalTime
     * @return \Coditron\Lixtask\Api\Data\SpinwheelInterface
     */
    public function setScheduleLocalTime($scheduleLocalTime);

    /**
     * Get Time Zone
     *
     * @return string|null
     */
    public function getTimeZone();

    /**
     * Set Time Zone
     *
     * @param string $timeZone
     * @return \Coditron\Lixtask\Api\Data\SpinwheelInterface
     */
    public function setTimeZone($timeZone);

   /**
    * Get IsActive.
    *
    * @return string
    */
    public function getStatus();

   /**
    * Set StartingPrice.
    */
    public function setStatus($status);
    
}
