<?php
/**
 * Grid GridInterface.
 * @category  Webkul
 * @package   Webkul_Grid
 * <AUTHOR>
 * @copyright Copyright (c) 2010-2017 Webkul Software Private Limited (https://webkul.com)
 * @license   https://store.webkul.com/license.html
 */

namespace Coditron\Lixtask\Api\Data;

interface GridInterface
{
    /**
     * Constants for keys of data array. Identical to the name of the getter in snake case.
     */
    const ENTITY_ID = 'entity_id';
    const TASK_ID = 'task_id';
    const ITEM_COST = 'item_cost';
	const ITEM_CURRENCY = 'item_currency';
    const ITEM_REWARD = 'item_reward';
    const IS_ACTIVE = 'is_active';
   

   /**
    * Get EntityId.
    *
    * @return int
    */
    public function getEntityId();

   /**
    * Set EntityId.
    */
    public function setEntityId($entityId);

   /**
     * Get id
     * @return string|null
     */
    public function getTaskId();

    /**
     * Set id
     * @param int $id
     * @return \Coditron\Lixtask\Api\Data\RewardInterface
     */
    public function setTaskId($task_id);

    /**
     * Get item_cost
     * @return string|null
     */
    public function getItemCost();

    /**
     * Set id
     * @param string $item_cost
     * @return \Coditron\Lixtask\Api\Data\RewardInterface
     */
    public function setItemCost($item_cost);

    /**
     * Get item_currency
     * @return string|null
     */
    public function getItemCurrency();

    /**
     * Set item_currency
     * @param string $item_currency
     * @return \Coditron\Lixtask\Api\Data\RewardInterface
     */
    public function setItemCurrency($item_currency);

    /**
     * Get item_reward
     * @return string|null
     */
    public function getItemReward();

    /**
     * Set id
     * @param string $item_reward
     * @return \Coditron\Lixtask\Api\Data\RewardInterface
     */
    public function setItemReward($item_reward);

    /**
    * Get IsActive.
    *
    * @return varchar
    */
    public function getIsActive();

   /**
    * Set StartingPrice.
    */
    public function setIsActive($isActive);
}
