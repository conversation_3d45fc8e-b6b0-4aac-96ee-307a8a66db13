<?php
declare(strict_types=1);

namespace Coditron\Lixtask\Model\ResourceModel\Customform;

class Collection extends \Magento\Framework\Model\ResourceModel\Db\Collection\AbstractCollection
{

    /**
     * @var string
     */
    protected $_idFieldName = 'id';
	protected $_previewFlag;
    /**
     * Define resource model
     *
     * @return void
     */
    protected function _construct()
    {
        $this->_init(
            \Coditron\Lixtask\Model\Customform::class,
            \Coditron\Lixtask\Model\ResourceModel\Customform::class
        );
        $this->_map['fields']['id'] = 'main_table.id';
    }
}

