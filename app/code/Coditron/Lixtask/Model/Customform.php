<?php
declare(strict_types=1);

namespace Coditron\Lixtask\Model;

use Coditron\Lixtask\Api\Data\CustomformInterface;
use Coditron\Lixtask\Api\Data\CustomformInterfaceFactory;
use Coditron\Lixtask\Model\ResourceModel\Customform\Collection;
use Magento\Framework\Api\DataObjectHelper;
use Magento\Framework\Model\Context;
use Magento\Framework\Registry;

class Customform extends \Magento\Framework\Model\AbstractModel
{
    protected $_eventPrefix = 'coditron_lixtask';

    public function __construct(
        Context $context,
        Registry $registry,
        protected readonly CustomformInterfaceFactory $customformDataFactory,
        protected readonly DataObjectHelper $dataObjectHelper,
        ResourceModel\Customform $resource,
        Collection $resourceCollection,
        array $data = []
    ) {
        parent::__construct($context, $registry, $resource, $resourceCollection, $data);
    }

    /**
     * Retrieve customform model with customform data
     * @return CustomformInterface
     */
    public function getDataModel()
    {
        $customformData = $this->getData();

        $customformDataObject = $this->customformDataFactory->create();
        $this->dataObjectHelper->populateWithArray(
            $customformDataObject,
            $customformData,
            CustomformInterface::class
        );

        return $customformDataObject;
    }
}

