<?php
/**
 * Languagepack Translation ResourceModel.
 * @category  Coditron
 * @package   Coditron_Languagepack
 * <AUTHOR>
 * @copyright Copyright (c) 2024 Coditron (https://www.coditron.com/)
 * @license    https://www.coditron.com/LICENSE.txt
 */
namespace Coditron\Lixtask\Model;

use Coditron\Lixtask\Api\Data\ScrachcardInterface;

class Scrachcard extends \Magento\Framework\Model\AbstractModel implements ScrachcardInterface
{
    /**
     * CMS page cache tag.
     */
    const CACHE_TAG = 'wk_scrachcard_records';

    /**
     * @var string
     */
    protected $_cacheTag = 'wk_scrachcard_records';

    /**
     * Prefix of model events names.
     *
     * @var string
     */
    protected $_eventPrefix = 'wk_scrachcard_records';

    /**
     * Initialize resource model.
     */
    protected function _construct()
    {
        $this->_init('Coditron\Lixtask\Model\ResourceModel\Scrachcard');
    }
    /**
     * Get EntityId.
     *
     * @return int
     */
    public function getId()
    {
        return $this->getData(self::ID);
    }

    /**
     * Set EntityId.
     */
    public function setId($id)
    {
        return $this->setData(self::ID, $id);
    }

    /**
     * Get Project ID.
     *
     * @return int|null
     */
    public function getProjectId()
    {
        return $this->getData(self::PROJECT_ID);
    }

    /**
     * Set Project ID.
     *
     * @param int $projectId
     * @return \Coditron\Lixtask\Api\Data\ScrachcardInterface
     */
    public function setProjectId($projectId)
    {
        return $this->setData(self::PROJECT_ID, $projectId);
    }

    /**
     * Get Title.
     *
     * @return string|null
     */
    public function getTitle()
    {
        return $this->getData(self::TITLE);
    }

    /**
     * Set Title.
     *
     * @param string $title
     * @return \Coditron\Lixtask\Api\Data\ScrachcardInterface
     */
    public function setTitle($title)
    {
        return $this->setData(self::TITLE, $title);
    }

    /**
     * Get Custom Currency Organisation ID.
     *
     * @return int|null
     */
    public function getCustomCurrencyOrganisationId()
    {
        return $this->getData(self::CUSTOM_CURRENCY_ORGANISATION_ID);
    }

    /**
     * Set Custom Currency Organisation ID.
     *
     * @param int $customCurrencyOrganisationId
     * @return \Coditron\Lixtask\Api\Data\ScrachcardInterface
     */
    public function setCustomCurrencyOrganisationId($customCurrencyOrganisationId)
    {
        return $this->setData(self::CUSTOM_CURRENCY_ORGANISATION_ID, $customCurrencyOrganisationId);
    }

    /**
     * Get Total Coins Available.
     *
     * @return int|null
     */
    public function getTotalCoinsAvailable()
    {
        return $this->getData(self::TOTAL_COINS_AVAILABLE);
    }

    /**
     * Set Total Coins Available.
     *
     * @param int $totalCoinsAvailable
     * @return \Coditron\Lixtask\Api\Data\ScrachcardInterface
     */
    public function setTotalCoinsAvailable($totalCoinsAvailable)
    {
        return $this->setData(self::TOTAL_COINS_AVAILABLE, $totalCoinsAvailable);
    }

    /**
     * Get Approval Type.
     *
     * @return string|null
     */
    public function getApprovalType()
    {
        return $this->getData(self::APPROVAL_TYPE);
    }

    /**
     * Set Approval Type.
     *
     * @param string $approvalType
     * @return \Coditron\Lixtask\Api\Data\ScrachcardInterface
     */
    public function setApprovalType($approvalType)
    {
        return $this->setData(self::APPROVAL_TYPE, $approvalType);
    }

    /**
     * Get Reward Amounts.
     *
     * @return string|null
     */
    public function getRewardAmounts()
    {
        return $this->getData(self::REWARD_AMOUNTS);
    }

    /**
     * Set Reward Amounts.
     *
     * @param string $rewardAmounts
     * @return \Coditron\Lixtask\Api\Data\ScrachcardInterface
     */
    public function setRewardAmounts($rewardAmounts)
    {
        return $this->setData(self::REWARD_AMOUNTS, $rewardAmounts);
    }

    /**
     * Get Reward Expires In.
     *
     * @return string|null
     */
    public function getRewardExpiresIn()
    {
        return $this->getData(self::REWARD_EXPIRES_IN);
    }

    /**
     * Set Reward Expires In.
     *
     * @param string $rewardExpiresIn
     * @return \Coditron\Lixtask\Api\Data\ScrachcardInterface
     */
    public function setRewardExpiresIn($rewardExpiresIn)
    {
        return $this->setData(self::REWARD_EXPIRES_IN, $rewardExpiresIn);
    }

    /**
     * Get Has Location Trigger.
     *
     * @return bool|null
     */
    public function getHasLocationTrigger()
    {
        return $this->getData(self::HAS_LOCATION_TRIGGER);
    }

    /**
     * Set Has Location Trigger.
     *
     * @param bool $hasLocationTrigger
     * @return \Coditron\Lixtask\Api\Data\ScrachcardInterface
     */
    public function setHasLocationTrigger($hasLocationTrigger)
    {
        return $this->setData(self::HAS_LOCATION_TRIGGER, $hasLocationTrigger);
    }

    /**
     * Get Location ID.
     *
     * @return int|null
     */
    public function getLocationId()
    {
        return $this->getData(self::LOCATION_ID);
    }

    /**
     * Set Location ID.
     *
     * @param int $locationId
     * @return \Coditron\Lixtask\Api\Data\ScrachcardInterface
     */
    public function setLocationId($locationId)
    {
        return $this->setData(self::LOCATION_ID, $locationId);
    }

    /**
     * Get Schedule Local Time.
     *
     * @return string|null
     */
    public function getScheduleLocalTime()
    {
        return $this->getData(self::SCHEDULE_LOCAL_TIME);
    }

    /**
     * Set Schedule Local Time.
     *
     * @param string $scheduleLocalTime
     * @return \Coditron\Lixtask\Api\Data\ScrachcardInterface
     */
    public function setScheduleLocalTime($scheduleLocalTime)
    {
        return $this->setData(self::SCHEDULE_LOCAL_TIME, $scheduleLocalTime);
    }

    /**
     * Get Time Zone.
     *
     * @return string|null
     */
    public function getTimeZone()
    {
        return $this->getData(self::TIME_ZONE);
    }

    /**
     * Set Time Zone.
     *
     * @param string $timeZone
     * @return \Coditron\Lixtask\Api\Data\ScrachcardInterface
     */
    public function setTimeZone($timeZone)
    {
        return $this->setData(self::TIME_ZONE, $timeZone);
    }

     /**
     * Get IsActive.
     *
     * @return varchar
     */
    public function getStatus()
    {
        return $this->getData(self::STATUS);
    }

    /**
     * Set IsActive.
     */
    public function setStatus($status)
    {
        return $this->setData(self::STATUS, $status);
    }
}