<?php
namespace Coditron\Lixtask\Model;

use Magento\Framework\Data\OptionSourceInterface;
use Coditron\Lixtask\Helper\Api;

class Orgid implements OptionSourceInterface
{
    /**
     * @var Api
     */
    protected $apiHelper;

    /**
     * Constructor
     *
     * @param Api $apiHelper
     */
    public function __construct(Api $apiHelper)
    {
        $this->apiHelper = $apiHelper;
    }

    /**
     * Get Grid row status type labels array.
     *
     * @return array
     */
    public function getOptionArray()
    {
        // Fetch custom currency organisation options
        $currencyOptions = $this->apiHelper->getWalletName();
        foreach ($currencyOptions as $option) {
            $options[$option['value']] = $option['label'];
        }

        return $options;
    }

    /**
     * Get Grid row status labels array with empty value for option element.
     *
     * @return array
     */
    public function getAllOptions()
    {
        $res = $this->getOptions();
        array_unshift($res, ['value' => '', 'label' => '']);
        return $res;
    }

    /**
     * Get Grid row type array for option element.
     *
     * @return array
     */
    public function getOptions()
    {
        $res = [];
        foreach ($this->getOptionArray() as $index => $value) {
            $res[] = ['value' => $index, 'label' => $value];
        }
        return $res;
    }

    /**
     * {@inheritdoc}
     */
    public function toOptionArray()
    {
        return $this->getOptions();
    }
}
