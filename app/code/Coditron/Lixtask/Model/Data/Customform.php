<?php
declare(strict_types=1);

namespace Coditron\Lixtask\Model\Data;

use Coditron\Lixtask\Api\Data\CustomformInterface;

class Customform extends \Magento\Framework\Api\AbstractExtensibleObject implements CustomformInterface
{
    /**
     * Get id
     * @return string|null
     */
    public function getId()
    {
        return $this->_get(self::ID);
    }

    /**
     * Set id
     * @param string $id
     * @return \Coditron\Lixtask\Api\Data\CustomformInterface
     */
    public function setId($id)
    {
        return $this->setData(self::ID, $id);
    }

    public function getRewardExpiresIn(): ?int
    {
        $exp = $this->_get(self::REWARD_EXPIRATION_IN);
        return $exp ? (int)$exp : null;
    }

    public function setRewardExpiresIn($expirationId): self
    {
        return $this->setData(self::REWARD_EXPIRATION_IN, $expirationId);
    }

    public function getTaskButtonLabel(): ?string
    {
        return $this->_get(self::LABEL);
    }

    public function setTaskButtonLabel(?string $label): self
    {
        return $this->setData(self::LABEL, $label);
    }

    public function getTitle()
    {
        return $this->_get(self::TITLE);
    }

    public function setTitle($title)
    {
        return $this->setData(self::TITLE, $title);
    }

    public function getDescription()
    {
        return $this->_get(self::DESCRIPTION);
    }

    public function setDescription($description)
    {
        return $this->setData(self::DESCRIPTION, $description);
    }

    public function getCustomCurrencyOrganisationId()
    {
        return $this->_get(self::CUSTOM_CURRENCY_ORG_ID);
    }

    public function setCustomCurrencyOrganisationId($currencyOrganisationId)
    {
        return $this->setData(self::CUSTOM_CURRENCY_ORG_ID, $currencyOrganisationId);
    }

    public function getPeriodAllowedForSubmissions(): ?string
    {
        return $this->_get(self::PERIOD_ALLOWED_SUBMISSIONS);
    }

    public function setPeriodAllowedForSubmissions(?string $value): self
    {
        return $this->setData(self::PERIOD_ALLOWED_SUBMISSIONS, $value);
    }

    public function getNumberOfSubmissionsAllowed()
    {
        return $this->_get(self::NUMBERR_SUBMISSIONS_ALLOWED);
    }

    public function setNumberOfSubmissionsAllowed($numberOfSubmissionsAllowed)
    {
        return $this->setData(self::NUMBERR_SUBMISSIONS_ALLOWED, $numberOfSubmissionsAllowed);
    }

    public function getIsStaffApproval()
    {
        return $this->_get(self::IS_STAFF_APPROVAL);
    }

    public function setIsStaffApproval($isStaffApproval)
    {
        return $this->setData(self::IS_STAFF_APPROVAL, $isStaffApproval);
    }
    public function getRewardType()
    {
        return $this->_get(self::REWARD_TYPE);
    }

    public function setRewardType($rewardType)
    {
        return $this->setData(self::REWARD_TYPE, $rewardType);
    }
    public function getProofType()
    {
        return $this->_get(self::PROOF_TYPE);
    }

    public function setProofType($proofType)
    {
        return $this->setData(self::PROOF_TYPE, $proofType);
    }
    public function getCoinsPerAction()
    {
        return $this->_get(self::COINS_PER_ACTION);
    }

    public function setCoinsPerAction($coins)
    {
        return $this->setData(self::COINS_PER_ACTION, $coins);
    }

    public function getXpPoints(): int
    {
        return (int)$this->_get(self::XP_POINTS);
    }

    public function setXpPoints($points)
    {
        return $this->setData(self::XP_POINTS, $points);
    }

    public function getTaskUrl()
    {
        return $this->_get(self::TASK_URL);
    }

    public function setTaskUrl($taskUrl)
    {
        return $this->setData(self::TASK_URL, $taskUrl);
    }

    public function getTitleShow(): string
    {
        return (string)$this->_get(self::TITLE_SHOW);
    }

    public function setTaskTitleShow(?string $titleShow): self
    {
        return $this->setData(self::TITLE_SHOW, $titleShow);
    }

    public function getTaskId()
    {
        return $this->_get(self::TASK_ID);
    }

    /**
     * Set task_id
     * @param string $task_id
     * @return \Coditron\Lixtask\Api\Data\CustomformInterface
     */
    public function setTaskId($task_id)
    {
        return $this->setData(self::TASK_ID, $task_id);
    }

    /**
     * Retrieve existing extension attributes object or create a new one.
     * @return \Coditron\Lixtask\Api\Data\CustomformExtensionInterface|null
     */
    public function getExtensionAttributes()
    {
        return $this->_getExtensionAttributes();
    }

    /**
     * Set an extension attributes object.
     * @param \Coditron\Lixtask\Api\Data\CustomformExtensionInterface $extensionAttributes
     * @return $this
     */
    public function setExtensionAttributes(
        \Coditron\Lixtask\Api\Data\CustomformExtensionInterface $extensionAttributes
    )
    {
        return $this->_setExtensionAttributes($extensionAttributes);
    }

    /**
     * Get first_name
     * @return string|null
     */
    public function getFirstName()
    {
        return $this->_get(self::FIRST_NAME);
    }

    /**
     * Set first_name
     * @param string $firstName
     * @return \Coditron\Lixtask\Api\Data\CustomformInterface
     */
    public function setFirstName($firstName)
    {
        return $this->setData(self::FIRST_NAME, $firstName);
    }

    /**
     * Get last_name
     * @return string|null
     */
    public function getLastName()
    {
        return $this->_get(self::LAST_NAME);
    }

    /**
     * Set last_name
     * @param string $lastName
     * @return \Coditron\Lixtask\Api\Data\CustomformInterface
     */
    public function setLastName($lastName)
    {
        return $this->setData(self::LAST_NAME, $lastName);
    }

    /**
     * Get email
     * @return string|null
     */
    public function getEmail()
    {
        return $this->_get(self::EMAIL);
    }

    /**
     * Set email
     * @param string $email
     * @return \Coditron\Lixtask\Api\Data\CustomformInterface
     */
    public function setEmail($email)
    {
        return $this->setData(self::EMAIL, $email);
    }

    /**
     * Get message
     * @return string|null
     */
    public function getMessage()
    {
        return $this->_get(self::MESSAGE);
    }

    /**
     * Set message
     * @param string $message
     * @return \Coditron\Lixtask\Api\Data\CustomformInterface
     */
    public function setMessage($message)
    {
        return $this->setData(self::MESSAGE, $message);
    }

    /**
     * Get status
     * @return string|null
     */
    public function getStatus()
    {
        return $this->_get(self::STATUS);
    }

    /**
     * Set status
     * @param string $status
     * @return \Coditron\Lixtask\Api\Data\CustomformInterface
     */
    public function setStatus($status)
    {
        return $this->setData(self::STATUS, $status);
    }

    /**
     * Get image
     * @return string|null
     */
    public function getImage()
    {
        return $this->_get(self::IMAGE);
    }

    /**
     * Set image
     * @param string $image
     * @return \Coditron\Lixtask\Api\Data\CustomformInterface
     */
    public function setImage($image)
    {
        return $this->setData(self::IMAGE, $image);
    }

    /**
     * Get created_at
     * @return string|null
     */
    public function getCreatedAt()
    {
        return $this->_get(self::CREATED_AT);
    }

    /**
     * Set created_at
     * @param string $createdAt
     * @return \Coditron\Lixtask\Api\Data\CustomformInterface
     */
    public function setCreatedAt($createdAt)
    {
        return $this->setData(self::CREATED_AT, $createdAt);
    }

    /**
     * Get phone
     * @return string|null
     */
    public function getPhone()
    {
        return $this->_get(self::PHONE);
    }

    /**
     * Set phone
     * @param string $phone
     * @return \Coditron\Lixtask\Api\Data\CustomformInterface
     */
    public function setPhone($phone)
    {
        return $this->setData(self::PHONE, $phone);
    }
}

