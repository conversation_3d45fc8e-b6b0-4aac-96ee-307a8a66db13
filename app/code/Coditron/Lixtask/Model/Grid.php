<?php

/**
 * Grid Grid Model.
 * @category  Webkul
 * @package   Webkul_Grid
 * <AUTHOR>
 * @copyright Copyright (c) 2010-2017 Webkul Software Private Limited (https://webkul.com)
 * @license   https://store.webkul.com/license.html
 */
namespace Coditron\Lixtask\Model;

use Coditron\Lixtask\Api\Data\GridInterface;

class Grid extends \Magento\Framework\Model\AbstractModel implements GridInterface
{
    /**
     * CMS page cache tag.
     */
    const CACHE_TAG = 'wk_grid_records';

    /**
     * @var string
     */
    protected $_cacheTag = 'wk_grid_records';

    /**
     * Prefix of model events names.
     *
     * @var string
     */
    protected $_eventPrefix = 'wk_grid_records';

    /**
     * Initialize resource model.
     */
    protected function _construct()
    {
        $this->_init('Coditron\Lixtask\Model\ResourceModel\Grid');
    }
    /**
     * Get EntityId.
     *
     * @return int
     */
    public function getEntityId()
    {
        return $this->getData(self::ENTITY_ID);
    }

    /**
     * Set EntityId.
     */
    public function setEntityId($entityId)
    {
        return $this->setData(self::ENTITY_ID, $entityId);
    }

    /**
     * Get Title.
     *
     * @return varchar
     */
    public function getTaskId()
    {
        return $this->getData(self::TASK_ID);
    }

    /**
     * Set Title.
     */
    public function setTaskId($task_id)
    {
        return $this->setData(self::TASK_ID, $task_id);
    }

    /**
     * Get getContent.
     *
     * @return varchar
     */
    public function getItemCost()
    {
        return $this->getData(self::ITEM_COST);
    }

    /**
     * Set Content.
     */
    public function setItemCost($item_cost)
    {
        return $this->setData(self::ITEM_COST, $item_cost);
    }

    /**
     * Get PublishDate.
     *
     * @return varchar
     */
    public function getItemCurrency()
    {
        return $this->getData(self::ITEM_CURRENCY);
    }

    /**
     * Set PublishDate.
     */
    public function setItemCurrency($item_currency)
    {
        return $this->setData(self::ITEM_CURRENCY, $item_currency);
    }

    /**
     * Get IsActive.
     *
     * @return varchar
     */
    public function getItemReward()
    {
        return $this->getData(self::ITEM_REWARD);
    }

    /**
     * Set IsActive.
     */
    public function setItemReward($item_reward)
    {
        return $this->setData(self::ITEM_REWARD, $item_reward);
    }
     /**
     * Get IsActive.
     *
     * @return varchar
     */
    public function getIsActive()
    {
        return $this->getData(self::IS_ACTIVE);
    }

    /**
     * Set IsActive.
     */
    public function setIsActive($isActive)
    {
        return $this->setData(self::IS_ACTIVE, $isActive);
    }

   
}
