<?php
declare(strict_types=1);

namespace Co<PERSON>ron\Lixtask\Model\Customform;

use AllowDynamicProperties;
use Coditron\Lixtask\Model\ResourceModel\Customform\CollectionFactory;
use Magento\Framework\App\Request\DataPersistorInterface;
use Magento\Framework\Filesystem;
use Magento\Framework\App\Filesystem\DirectoryList;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Coditron\Lixtask\Helper\Api;

#[AllowDynamicProperties]
class DataProvider extends \Magento\Ui\DataProvider\AbstractDataProvider
{
    protected $loadedData;
    protected $collection;
    protected $filesystem;
    protected $dataPersistor;
    protected $storeManager;

    public function __construct(
        $name,
        $primaryFieldName,
        $requestFieldName,
        CollectionFactory $collectionFactory,
        DataPersistorInterface $dataPersistor,
        \Magento\Store\Model\StoreManagerInterface $storeManager,
        Filesystem $filesystem,
        ScopeConfigInterface $scopeConfig,
        Api $apiHelper,
        array $meta = [],
        array $data = []
    ) {
        $this->collection = $collectionFactory->create();
        $this->dataPersistor = $dataPersistor;
        $this->filesystem = $filesystem;
        $this->storeManager = $storeManager;
        $this->scopeConfig = $scopeConfig;
        $this->apiHelper = $apiHelper;
        parent::__construct($name, $primaryFieldName, $requestFieldName, $meta, $data);
    }

    /**
     * Get data
     *
     * @return array
     */
    public function getData()
    {
        if (isset($this->loadedData)) {
            return $this->loadedData;
        }
		$mediaDirectory = $this->filesystem->getDirectoryRead(DirectoryList::MEDIA);
		$destinationPath = $mediaDirectory->getAbsolutePath('coditron/customform');
        $items = $this->collection->getItems();
        foreach ($items as $model) {
            $itemData = $model->getData();
            if ($model->getImage()) {
                $imageName = $itemData['image']; // Your database field
                //unset($itemData['image']);
                $itemData['image'] = array(
                    array(
                        'name'  =>  $imageName,
                        'url'   =>  $this->storeManager
                    ->getStore()
                    ->getBaseUrl(
                        \Magento\Framework\UrlInterface::URL_TYPE_MEDIA
                    ).'coditron/customform'.$itemData['image'] // Should return a URL to view the image. For example, http://domain.com/pub/media/../../imagename.jpeg
                    )
                );
            }
            $this->loadedData[$model->getId()] = $itemData;
        }
        $data = $this->dataPersistor->get('cdadmincustomform');

        if (!empty($data)) {
            $model = $this->collection->getNewEmptyItem();
            $model->setData($data);
            $this->loadedData[$model->getId()] = $model->getData();
            $this->dataPersistor->clear('cdadmincustomform');
        }

        return $this->loadedData;
    }

     /**
     * Code for Add "Use Default Value" in UI Form
     */
    public function getMeta()
    {
        $meta = parent::getMeta();
        $defaultProjectId =  $this->apiHelper->getProjectId();

        $meta['general']['children']['project_id']['arguments']['data']['config']['default'] = $defaultProjectId;
        $meta['general']['children']['project_id']['arguments']['data']['config']['disabled'] = 1;

        return $meta;
    }
}
