<?php
declare(strict_types=1);

namespace Coditron\Lixtask\Controller\Adminhtml\Customform;

use AllowDynamicProperties;
use Magento\Framework\Exception\LocalizedException;
use Magento\Store\Model\StoreManagerInterface;
use Coditron\Lixtask\Model\CustomformFactory;


#[AllowDynamicProperties]
class Save extends \Magento\Backend\App\Action
{

    protected $dataPersistor;

    /**
     * @param \Magento\Backend\App\Action\Context $context
     * @param \Magento\Framework\App\Request\DataPersistorInterface $dataPersistor
     */
    public function __construct(
        \Magento\Backend\App\Action\Context $context,
        \Magento\Framework\App\Request\DataPersistorInterface $dataPersistor,
        \Coditron\Lixtask\Helper\Api $apiHelper,
        StoreManagerInterface $storeManager,
        CustomformFactory $customformFactory
    ) {
        $this->dataPersistor = $dataPersistor;
        $this->apiHelper = $apiHelper;
        $this->storeManager = $storeManager;
        $this->customformFactory = $customformFactory;
        parent::__construct($context);
    }

    /**
     * Save action
     *
     * @return \Magento\Framework\Controller\ResultInterface
     */
    public function execute()
    {
        /** @var \Magento\Backend\Model\View\Result\Redirect $resultRedirect */
        $resultRedirect = $this->resultRedirectFactory->create();
        $data = $this->getRequest()->getPostValue();
        if ($data) {
            $id = $this->getRequest()->getParam('id');

            $model = $this->_objectManager->create(\Coditron\Lixtask\Model\Customform::class)->load($id);
            if (!$model->getId() && $id) {
                $this->messageManager->addErrorMessage(__('This Customform no longer exists.'));
                return $resultRedirect->setPath('*/*/');
            }

			if (isset($data['image'][0]['name']) && isset($data['image'][0]['tmp_name'])) {
                $data['image'] =$data['image'][0]['name'];
                $this->imageUploader = \Magento\Framework\App\ObjectManager::getInstance()->get(
                'Coditron\Lixtask\CustomformImageUpload'
            );
                $this->imageUploader->moveFileFromTmp($data['image']);
            } elseif (isset($data['image'][0]['name']) && !isset($data['image'][0]['tmp_name'])) {
                $data['image'] = $data['image'][0]['name'];
            } else {
                $data['image'] = null;
            }
            $model->setData($data);

            $iurl = $this->storeManager->getStore()->getBaseUrl(\Magento\Framework\UrlInterface::URL_TYPE_MEDIA) . 'coditron/customform' . $data['image'];


            try {
                $model->save();

                // Extract specific data to send to the API
                $apiData = [
                    'project_id' => $data['project_id'],
                    'custom_currency_organisation_id' => $data['custom_currency_organisation_id'],
                    'title' => $data['task_title'],
                    'reward_type' => $data['reward_type'],
                    'coins_per_action' => $data['coins_per_action'],
                    'approval_type' => $data['approval_type'],
                    'proof_type' => $data['proof_type'],
                    'task_method' => $data['task_method'],
                    'status' => $data['status'],
                    'total_coins_available'=> $data['total_coins_available'],
                    'total_task_submission'=> $data['total_task_submission'],
                    'parent_id'=> $data['parent_id'],
                    'description'=> $data['description'],
                    'reward_expires_in'=> $data['reward_expires_in'],
                    'is_staff_approval'=> $data['is_staff_approval'],
                    'shared_organisation_id'=> $data['shared_organisation_id'],
                    'number_of_submissions_allowed_per_period'=> $data['number_of_submissions_allowed_per_period'],
                    'number_of_submissions_allowed' => $data['number_of_submissions_allowed'],
                    'period_allowed_for_submissions' => $data['period_allowed_for_submissions'],
                    'logo_url' => $iurl
                ];

                // Pass data to Update API
                if ($id) {
                    $upTaskId = $model->getTaskId();
                    $apiResponse = $this->apiHelper->updateLixTask($upTaskId, $apiData);
                } else {
                // Pass data to Save API
                    $apiResponse = $this->apiHelper->setLixTask($apiData);
                    $apiTaskId = $apiResponse['data']['id'];
                    $apiTaskCurrency = $apiResponse['data']['currency'];
                    if ($apiTaskId) {
                            $customformModel = $this->customformFactory->create();
                            $customformModel->load($model->getId()); // Load the existing model
                            $customformModel->setTaskId($apiTaskId); // Set the task ID
                            $customformModel->setCustomCurrencyOrganisationId($apiTaskCurrency);
                            $customformModel->save(); // Save the updated model
                    }
                }

                $this->messageManager->addSuccessMessage(__('You saved the Customform.'));
                $this->dataPersistor->clear('cdadmincustomform');

                if ($this->getRequest()->getParam('back')) {
                    return $resultRedirect->setPath('*/*/edit', ['id' => $model->getId()]);
                }
                return $resultRedirect->setPath('*/*/');
            } catch (LocalizedException $e) {
                $this->messageManager->addErrorMessage($e->getMessage());
            } catch (\Exception $e) {
                $this->messageManager->addExceptionMessage($e, __('Something went wrong while saving the Customform.'));
            }

            $this->dataPersistor->set('cdadmincustomform', $data);
            return $resultRedirect->setPath('*/*/edit', ['id' => $this->getRequest()->getParam('id')]);
        }
        return $resultRedirect->setPath('*/*/');
    }
}

