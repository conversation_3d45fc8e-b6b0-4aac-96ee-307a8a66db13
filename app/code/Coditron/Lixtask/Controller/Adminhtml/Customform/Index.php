<?php
declare(strict_types=1);

namespace Coditron\Lixtask\Controller\Adminhtml\Customform;

class Index extends \Magento\Backend\App\Action
{

    protected $resultPageFactory;

    /**
     * Constructor
     *
     * @param \Magento\Backend\App\Action\Context $context
     * @param \Magento\Framework\View\Result\PageFactory $resultPageFactory
     */
    public function __construct(
        \Magento\Backend\App\Action\Context $context,
        \Magento\Framework\View\Result\PageFactory $resultPageFactory
    ) {
        $this->resultPageFactory = $resultPageFactory;
        parent::__construct($context);
    }

    /**
     * Index action
     *
     * @return \Magento\Framework\Controller\ResultInterface
     */
    public function execute()
    {
        $resultPage = $this->resultPageFactory->create();
        $resultPage->setActiveMenu('Coditron::top_level');
        $resultPage->addBreadcrumb(__('Lix Task'), __('Lix Task'));
        $resultPage->addBreadcrumb(__('Manage Lix Task'), __('Manage Lix Task'));
        $resultPage->getConfig()->getTitle()->prepend(__("Manage Task"));

        return $resultPage;
    }
}

