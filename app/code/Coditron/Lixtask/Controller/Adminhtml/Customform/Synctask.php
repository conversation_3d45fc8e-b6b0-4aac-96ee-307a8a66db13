<?php
declare(strict_types=1);

namespace Coditron\Lixtask\Controller\Adminhtml\Customform;

use AllowDynamicProperties;
use Coditron\Lixtask\Model\CustomformFactory;
use Coditron\Lixtask\Model\ResourceModel\Customform;
use Coditron\Lixtask\Helper\Api;
use Magento\Store\Model\StoreManagerInterface;

#[AllowDynamicProperties]
class Synctask extends \Magento\Backend\App\Action
{
    /**
     * @param \Magento\Backend\App\Action\Context $context
     * @param \Magento\Framework\ObjectManagerInterface $objectManager
     */
    public function __construct(
        \Magento\Backend\App\Action\Context $context,
        \Coditron\Lixtask\Model\CustomformFactory $customformFactory,
        \Coditron\Lixtask\Model\ResourceModel\Customform $customResource,
        \Coditron\Lixtask\Helper\Api $dataHelper,
        \Coditron\Lixtask\Model\Customform $customformManager,
        StoreManagerInterface $storeManager,
        \Magento\Framework\View\Result\PageFactory $resultPageFactory
        ) {
        $this->customformFactory = $customformFactory;
        $this->customResource = $customResource;
        $this->dataHelper = $dataHelper;
        $this->storeManager = $storeManager;
        $this->customformManager = $customformManager;
        $this->resultPageFactory = $resultPageFactory;
        parent::__construct($context);
    }

    /**
     * Save action
     *
     * @return \Magento\Framework\Controller\ResultInterface
     */
    public function execute()
    {
        $projectId = $this->dataHelper->getProjectId();
        $url = $this->dataHelper->getSiteUrl();
        $pn =1;
        $nextUrl = $url."projects/".$projectId."/tasks/?page=".$pn;
        $synced = 0;
        $taskresponce = $this->dataHelper->getAllLixTaskList($projectId,$nextUrl);
        $oparray = array();
        while($taskresponce != null){
            $nextUrl = $url."projects/".$projectId."/tasks/?page=".$pn;
            $taskresponce = $this->dataHelper->getAllLixTaskList($projectId,$nextUrl);
            if($taskresponce != null){ $oparray = array_merge($oparray, $taskresponce);}
            $pn++;
        }

        if($oparray){
            foreach ($oparray as $task) {
                $data = array();
                $data['task_id'] = $task['id'];
                $data['task_title'] = $task['title'];
                $data['description'] = $task['description'];
                $data['currency'] = $task['currency'];
                $data['project_id']= $this->dataHelper->getProjectId();
                $data['reward_type'] = $task['reward_type'];
                $data['approval_type'] = $task['approval_type'];
                $data['proof_type'] = $task['proof_type'];
                $data['created_at'] = $task['created_at'];
                $data['coins_per_action'] = $task['coins_per_action'];
                $data['total_task_submission'] = $task['total_task_submission'];
                $data['status'] = $task['status'];
                $data['task_method'] = $task['task_method'];
                $data['shared_organisation_id'] = $task['shared_organisation_id'];
                $data['is_staff_approval'] = $task['is_staff_approval'];
                $data['number_of_submissions_allowed'] = $task['number_of_submissions_allowed'];
                $data['number_of_submissions_allowed_per_period'] = $task['number_of_submissions_allowed_per_period'];
                $data['custom_currency_organisation_id'] = $task['custom_currency_organisation_id'];
                //$data['reward_expires_in'] = $task['reward_expires_in'];
                // $data['period_allowed_for_submissions'] = $task['period_allowed_for_submissions'];
                // $data['task_url'] = $task['task_url'];
                // $data['task_button_label'] = $task['task_button_label'];
                //$data['total_coins_available'] = $task['total_coins_available'];
                // $data['image'] = $task['image'];
                //$projectid= array();
                //$projectid= $task['project'];
                //$data['parent_id']=$projectid['organisation']['parent_id'];

                $customModel = $this->customformManager->getCollection()->addFieldToFilter('task_id', $task['id']);
                $collectionSize = $customModel->getSize();

                if($collectionSize == 1){
                    foreach($customModel as $task){
                        if($data){
                            $task->setData('task_title', $data['task_title']);
                            $task->setData('description', $data['description']);
                            $task->setData('coins_per_action', $data['coins_per_action']);
                            $task->setData('currency', $data['currency']);
                            $task->setData('project_id', $data['project_id']);
                            $task->setData('reward_type', $data['reward_type']);
                            $task->setData('approval_type', $data['approval_type']);
                            $task->setData('proof_type', $data['proof_type']);
                            $task->setData('created_at', $data['created_at']);
                            $task->setData('total_task_submission', $data['total_task_submission']);
                            $task->setData('status', $data['status']);
                            $task->setData('task_method', $data['task_method']);
                            $task->setData('is_staff_approval', $data['is_staff_approval']);
                            $task->setData('shared_organisation_id', $data['shared_organisation_id']);
                            $task->setData('custom_currency_organisation_id', $data['custom_currency_organisation_id']);
                            $task->setData('number_of_submissions_allowed', $data['number_of_submissions_allowed']);
                            $task->setData('number_of_submissions_allowed_per_period', $data['number_of_submissions_allowed_per_period']);
                            $task->save();
                            $synced++;
                        }
                    }
                }else{
                    $this->customformManager->setData($data);
                    $this->customformManager->save();
                    $synced++;
                }
            }
        }

        if ($synced > 0) {
            $this->messageManager->addSuccessMessage(__('Task(s) %1 updated Successfully', $synced));
        }

        $resultRedirect = $this->resultRedirectFactory->create();
        return $resultRedirect->setPath('*/*/');
    }
}
