<?php
/**
 * Webkul Grid Record Delete Controller.
 * @category  Webkul
 * @package   Webkul_Grid
 * <AUTHOR>
 * @copyright Copyright (c) 2010-2017 Webkul Software Private Limited (https://webkul.com)
 * @license   https://store.webkul.com/license.html
 */
namespace Coditron\Lixtask\Controller\Adminhtml\Scrachcard;

use Magento\Framework\Controller\ResultFactory;
use Magento\Backend\App\Action\Context;
use Magento\Ui\Component\MassAction\Filter;
use Coditron\Lixtask\Model\ResourceModel\Scrachcard\CollectionFactory;

class MassDisable extends \Magento\Backend\App\Action
{
    /**
     * Massactions filter.
     * @var Filter
     */
    protected $_filter;

    /**
     * @var CollectionFactory
     */
    protected $_collectionFactory;

    /**
     * @param Context           $context
     * @param Filter            $filter
     * @param CollectionFactory $collectionFactory
     */
    public function __construct(
        Context $context,
        Filter $filter,
        CollectionFactory $collectionFactory
    ) {

        $this->_filter = $filter;
        $this->_collectionFactory = $collectionFactory;
        parent::__construct($context);
    }

    /**
     * @return \Magento\Backend\Model\View\Result\Redirect
     */
    public function execute()
    {
        $collection = $this->_filter->getCollection($this->_collectionFactory->create());
        $recordUpdate= 0;
        foreach ($collection->getItems() as $record) {
            // Load the translation model by ID
            $scratchcardModel = $this->_objectManager->create(\Coditron\Lixtask\Model\Scrachcard::class)->load($record->getId());
            // Check if the model exists
            if ($scratchcardModel->getId()) {
                // Delete the model
                $scratchcardModel->setStatus(0);
                $scratchcardModel->save();
                $recordUpdate++;
            }
        }
        $this->messageManager->addSuccess(__('A total of %1 record(s) have been disabled.', $recordUpdate));
    
        return $this->resultFactory->create(ResultFactory::TYPE_REDIRECT)->setPath('*/*/index');
    }

    /**
     * Check Category Map recode delete Permission.
     * @return bool
     */
    protected function _isAllowed()
    {
        return $this->_authorization->isAllowed('Coditron_Lixtask::row_data_enable');
    }
}
