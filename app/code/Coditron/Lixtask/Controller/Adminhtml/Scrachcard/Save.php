<?php
/**
 * Language translation Record save Controller.
 * @category  Coditron
 * @package   Coditron_Lixtask
 * <AUTHOR>
 * @copyright Copyright (c) 2024 Coditron (https://www.coditron.com/)
 * @license   https://www.coditron.com/LICENSE.txt
 */
namespace Coditron\Lixtask\Controller\Adminhtml\Scrachcard;

use AllowDynamicProperties;
use Magento\Backend\App\Action;
use Coditron\Lixtask\Model\ScrachcardFactory;
use Coditron\Lixtask\Helper\Api;
use Coditron\Lixtask\Model\CustomformFactory;
use Magento\Store\Model\StoreManagerInterface;
use Endroid\QrCode\Builder\Builder;
use Endroid\QrCode\Writer\PngWriter;

#[AllowDynamicProperties]
class Save extends Action
{
    /**
     * @var ScrachcardFactory
     */
    protected $scrachcardFactory;

    /**
     * @var Api
     */
    protected $apiHelper;

    /**
     * @var CustomformFactory
     */
    protected $customformFactory;

    /**
     * @param Action\Context $context
     * @param ScrachcardFactory $scrachcardFactory
     * @param Api $apiHelper
     * @param CustomformFactory $customformFactory
     */
    public function __construct(
        Action\Context $context,
        ScrachcardFactory $scrachcardFactory,
        Api $apiHelper,
        CustomformFactory $customformFactory,
        StoreManagerInterface $storeManager
    ) {
        parent::__construct($context);
        $this->scrachcardFactory = $scrachcardFactory;
        $this->apiHelper = $apiHelper;
        $this->customformFactory = $customformFactory;
        $this->_storeManager = $storeManager;
    }

    /**
     * @SuppressWarnings(PHPMD.CyclomaticComplexity)
     * @SuppressWarnings(PHPMD.NPathComplexity)
     */
    public function execute()
    {
        $data = $this->getRequest()->getPostValue();
        if (!$data) {
            $this->_redirect('cdadmincustomform/scrachcard/create');
            return;
        }

        $writer = new \Zend_Log_Writer_Stream(BP . '/var/log/SaveScratch.log');
        $logger = new \Zend_Log();
        $logger->addWriter($writer);
        $logger->info("In Here");

        try {
            $rowData = $this->scrachcardFactory->create();
            $rowData->setData($data);

            if (isset($data['id'])) {
                $rowData->setId($data['id']);
            }

            $rowData->save();

            $rewardAmounts = array_map('trim', explode(',', $data['reward_amounts']));

            $apiData = [
                'project_id' => $data['project_id'],
                'custom_currency_organisation_id' => $data['custom_currency_organisation_id'],
                'title' => $data['title'],
                'total_coins_available' => $data['total_coins_available'],
                'approval_type' => $data['approval_type'],
                'reward_expires_in' => $data['reward_expires_in'],
                'has_location_trigger' => true,
                'reward_amounts' => $rewardAmounts,
                'locations' => [
                    [
                        'location_id' => $data['location_id'],
                        'time_zone' => $data['time_zone'],
                        'schedule_local_time' => $data['schedule_local_time']
                    ]
                ],
            ];

            // Pass data to Save API
            $apiResponse = $this->apiHelper->createScratchCard($apiData);
            $logger->info(print_r($apiResponse, true) . ' apiResponse');

            if ($apiResponse) {
                $apiTaskId = $apiResponse['data']['id'];
                // $apiTaskCurrency = $apiResponse['data']['currency'];

                $storeUrl = $this->_storeManager->getStore()->getBaseUrl();
                $apiTaskLink = $storeUrl . 'lixreward/Scratchcard/get/' . $apiTaskId;

                $rowData->setTaskId($apiTaskId);
                // $rowData->setCustomCurrencyOrganisationId($apiTaskCurrency);

                $customformModel = $this->customformFactory->create();
                $customformModel->load($rowData->getId());

                // Generate QR code image
                $qrBuilder = Builder::create()
                    ->writer(new PngWriter())
                    ->data($apiTaskLink)
                    ->size(150)
                    ->margin(10)
                    ->build();

                // Ensure the directory exists
                $mediaDirectory = $this->_objectManager->get('\Magento\Framework\Filesystem')
                    ->getDirectoryRead(\Magento\Framework\App\Filesystem\DirectoryList::MEDIA)
                    ->getAbsolutePath();

                $qrCodeDirectory = $mediaDirectory . 'scratchcard_qr_codes/';
                if (!file_exists($qrCodeDirectory)) {
                    mkdir($qrCodeDirectory, 0777, true);
                }

                // Save QR code image to media folder
                $qrCodeFilePath = $qrCodeDirectory . $apiTaskId . '.png';
                $qrBuilder->saveToFile($qrCodeFilePath);

                // Save QR code URL to model
                $qrCodeUrl = $this->_storeManager->getStore()->getBaseUrl(\Magento\Framework\UrlInterface::URL_TYPE_MEDIA) . 'scratchcard_qr_codes/' . $apiTaskId . '.png';

                $logger->info('QR Code Image Path: ' . $qrCodeFilePath);

                $rowData->setQrCodeUrl($qrCodeUrl);
                $rowData->save();
            }

            $this->messageManager->addSuccess(__('Scratch Card data has been successfully saved.'));
        } catch (\Exception $e) {
            $this->messageManager->addError(__($e->getMessage()));
            $logger->err($e->getMessage());
        }

        $this->_redirect('cdadmincustomform/scrachcard/index');
    }

    /**
     * @return bool
     */
    protected function _isAllowed()
    {
        return $this->_authorization->isAllowed('Coditron_Lixtask::save');
    }
}
