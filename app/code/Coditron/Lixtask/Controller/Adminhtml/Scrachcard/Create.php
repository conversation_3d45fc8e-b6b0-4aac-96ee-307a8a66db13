<?php
/**
 * Languagepack add record of language controller.
 * @category  Coditron
 * @package   Coditron_Lixtask
 * <AUTHOR>
 * @copyright Copyright (c) 2024 Coditron (https://www.coditron.com/)
 * @license    https://www.coditron.com/LICENSE.txt
 */
namespace Coditron\Lixtask\Controller\Adminhtml\Scrachcard;

use Magento\Framework\Controller\ResultFactory;

class Create extends \Magento\Backend\App\Action
{
    /**
     * @var \Magento\Framework\Registry
     */
    private $coreRegistry;

     /**
     * @var \Coditron\Lixtask\Model\ScrachcardFactory
     */
    private $scrachcardFactory;


    /**
     * @param \Magento\Backend\App\Action\Context $context
     * @param \Magento\Framework\Registry $coreRegistry,
     * @param \Coditron\Lixtask\Model\ScrachcardFactory $scrachcardFactory
     */
    public function __construct(
        \Magento\Backend\App\Action\Context $context,
        \Magento\Framework\Registry $coreRegistry,
        \Coditron\Lixtask\Model\ScrachcardFactory $scrachcardFactory
    ) {
        parent::__construct($context);
        $this->coreRegistry = $coreRegistry;
        $this->scrachcardFactory = $scrachcardFactory;
    }

    /**
     * Mapped Grid List page.
     * @return \Magento\Backend\Model\View\Result\Page
     */
    public function execute()
    {
         $rowId = (int) $this->getRequest()->getParam('id');
         $rowData = $this->scrachcardFactory->create();
         if ($rowId) {
             // Load the existing record based on the provided ID
             $rowData = $this->scrachcardFactory->create()->load($rowId);

             // Check if the record exists
             if (!$rowData->getId()) {
                 // If the record doesn't exist, display an error message and redirect
                 $this->messageManager->addError(__('Srachcard data no longer exists.'));
                 $this->_redirect('cdadmincustomform/scrachcard/index');
                 return;
             }
         }

        // // Register the data in the registry
         $this->coreRegistry->register('scrachcard_data', $rowData);

        // Prepare the result page
        $resultPage = $this->resultFactory->create(ResultFactory::TYPE_PAGE);
        $title = $rowId ? __('Edit Scratch Card').$rowData->getTitle() : __('Add Scratch Card');
      
        $resultPage->getConfig()->getTitle()->prepend($title);
        return $resultPage;
    }

    protected function _isAllowed()
    {
        return $this->_authorization->isAllowed('Coditron_Lixtask::create_scrach_card');
    }
}
