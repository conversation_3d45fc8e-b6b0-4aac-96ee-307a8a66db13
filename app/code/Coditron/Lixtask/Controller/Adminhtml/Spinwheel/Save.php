<?php
/**
 * Language translation Record save Controller.
 * @category  Coditron
 * @package   Coditron_Lixtask
 * <AUTHOR>
 * @copyright Copyright (c) 2024 Coditron (https://www.coditron.com/)
 * @license    https://www.coditron.com/LICENSE.txt
 */
namespace Coditron\Lixtask\Controller\Adminhtml\Spinwheel;

use AllowDynamicProperties;
use Coditron\Lixtask\Helper\Api;
use Coditron\Lixtask\Model\CustomformFactory;
use Magento\Store\Model\StoreManagerInterface;
use Endroid\QrCode\Builder\Builder;
use Endroid\QrCode\Writer\PngWriter;

#[AllowDynamicProperties] class Save extends \Magento\Backend\App\Action
{
    /**
     * @var Coditron\Lixtask\Model\SpinwheelFactory
     */
    var $spinwheelFactory;

     /**
     * @var Api
     */
    protected $apiHelper;

    /**
     * @var CustomformFactory
     */
    protected $customformFactory;

    /**
     * @param \Magento\Backend\App\Action\Context $context
     * @param \Coditron\Lixtask\Model\SpinwheelFactory $spinwheelFactory
     * @param Api $apiHelper
     * @param CustomformFactory $customformFactory
     * @param StoreManagerInterface $storeManager
     */
    public function __construct(
        \Magento\Backend\App\Action\Context $context,
        \Coditron\Lixtask\Model\SpinwheelFactory $spinwheelFactory,
        Api $apiHelper,
        CustomformFactory $customformFactory,
        StoreManagerInterface $storeManager
    ) {
        parent::__construct($context);
        $this->spinwheelFactory = $spinwheelFactory;
        $this->apiHelper = $apiHelper;
        $this->customformFactory = $customformFactory;
        $this->_storeManager = $storeManager;
    }

    /**
     * @SuppressWarnings(PHPMD.CyclomaticComplexity)
     * @SuppressWarnings(PHPMD.NPathComplexity)
     */
    public function execute()
    {
        $writer = new \Zend_Log_Writer_Stream(BP . '/var/log/SpinSave.log');
        $logger = new \Zend_Log();
        $logger->addWriter($writer);

        $data = $this->getRequest()->getPostValue();

        if (!$data) {
            $this->_redirect('cdadmincustomform/spinwheel/createspinwheel');
            return;
        }

        try {
            $rowData = $this->spinwheelFactory->create();
            $rowData->setData($data);
            if (isset($data['id'])) {
                $rowData->setId($data['id']);
            }
            $existingTaskId = $rowData->getTaskId();
            $rowData->save();


        if (empty($existingTaskId)) {
            $rewardAmounts = array_map('trim', explode(',', $data['reward_amounts']));

            $apiData = [
                'project_id' => $data['project_id'],
                'custom_currency_organisation_id' => $data['custom_currency_organisation_id'],
                'title' => $data['title'],
                'total_coins_available' => $data['total_coins_available'],
                'approval_type' => $data['approval_type'],
                'reward_expires_in' => $data['reward_expires_in'],
                'has_location_trigger' => true,
                'reward_amounts' => $rewardAmounts,
                'location_id' => $data['location_id'],
                'time_zone' => $data['time_zone'],
                'schedule_local_time' => $data['schedule_local_time']
            ];

            $apiResponse = $this->apiHelper->createSpinWheel($apiData);
            $logger->info(print_r($apiResponse, true) . 'apiResponse');

            if($apiResponse['success'] == true){
                $apiTaskId = $apiResponse['data']['id'];
                $logger->info(print_r($apiTaskId, true) . 'apiTaskId');
                $apiTaskCurrency = $apiResponse['data']['currency'];
                $logger->info(print_r($apiTaskCurrency, true) . 'apiTaskCurrency');
               // $apiTaskLink = $apiResponse['data']['task_link'];
                $apiTaskLink = $this->_storeManager->getStore()->getBaseUrl().'lixreward/spinwheel/get/'.$apiTaskId;
                $rowData->setTaskId($apiTaskId);
               // $rowData->setCustomCurrencyOrganisationId($apiTaskCurrency);
            }
        }else{
                $apiTaskId = $existingTaskId;
                $apiTaskLink = $rowData->getTaskLink();
            }
            $customformModel = $this->customformFactory->create();
            $customformModel->load($rowData->getId());

             // Ensure the directory exists
             $mediaDirectory = $this->_objectManager->get('\Magento\Framework\Filesystem')
             ->getDirectoryRead(\Magento\Framework\App\Filesystem\DirectoryList::MEDIA)
             ->getAbsolutePath();

            $qrCodeDirectory = $mediaDirectory . 'spinwheel_qr_codes/';
            if (!file_exists($qrCodeDirectory)) {
                mkdir($qrCodeDirectory, 0777, true);
            }

            // Save QR code image to media folder
            $qrCodeFilePath = $qrCodeDirectory . $apiTaskId . '.png';
            if (!file_exists($qrCodeFilePath)) {
              // Generate QR code image
              $qrBuilder = Builder::create()
              ->writer(new PngWriter())
              ->data($apiTaskLink)
              ->size(150)
              ->margin(10)
              ->build();

            $qrBuilder->saveToFile($qrCodeFilePath);
            }

            // Save QR code URL to model
            $qrCodeUrl = $this->_storeManager->getStore()->getBaseUrl(\Magento\Framework\UrlInterface::URL_TYPE_MEDIA) . 'spinwheel_qr_codes/' . $apiTaskId . '.png';

            $rowData->setQrCodeUrl($qrCodeUrl);

            $rowData->save();
            $this->messageManager->addSuccess(__('Spinwheel data has been successfully saved.'));
        } catch (\Exception $e) {
            $this->messageManager->addError(__($e->getMessage()));
        }
        $this->_redirect('cdadmincustomform/spinwheel/index');
    }

    /**
     * @return bool
     */
    protected function _isAllowed()
    {
        return $this->_authorization->isAllowed('Coditron_Lixtask::save');
    }
}
