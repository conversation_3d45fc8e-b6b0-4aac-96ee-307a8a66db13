<?php
/**
 * Languagepack add record of language controller.
 * @category  Coditron
 * @package   Coditron_Lixtask
 * <AUTHOR>
 * @copyright Copyright (c) 2024 Coditron (https://www.coditron.com/)
 * @license   https://www.coditron.com/LICENSE.txt
 */
namespace Coditron\Lixtask\Controller\Adminhtml\Spinwheel;

use Magento\Framework\Controller\ResultFactory;

class Createspinwheel extends \Magento\Backend\App\Action
{
    /**
     * @var \Magento\Framework\Registry
     */
    private $coreRegistry;

     /**
     * @var \Coditron\Lixtask\Model\SpinwheelFactory
     */
    private $spinwheelFactory;


    /**
     * @param \Magento\Backend\App\Action\Context $context
     * @param \Magento\Framework\Registry $coreRegistry,
     * @param \Coditron\Lixtask\Model\SpinwheelFactory $spinwheelFactory
     */
    public function __construct(
        \Magento\Backend\App\Action\Context $context,
        \Magento\Framework\Registry $coreRegistry,
        \Coditron\Lixtask\Model\SpinwheelFactory $spinwheelFactory
    ) {
        parent::__construct($context);
        $this->coreRegistry = $coreRegistry;
        $this->spinwheelFactory = $spinwheelFactory;
    }

    /**
     * Mapped Grid List page.
     * @return \Magento\Backend\Model\View\Result\Page
     */
    public function execute()
    {
         $rowId = (int) $this->getRequest()->getParam('id');
         $rowData = $this->spinwheelFactory->create();
         if ($rowId) {
             // Load the existing record based on the provided ID
             $rowData = $this->spinwheelFactory->create()->load($rowId);

             // Check if the record exists
             if (!$rowData->getId()) {
                 // If the record doesn't exist, display an error message and redirect
                 $this->messageManager->addError(__('Spinwheel data no longer exists.'));
                 $this->_redirect('cdadmincustomform/spinwheel/index');
                 return;
             }
         }

        // // Register the data in the registry
         $this->coreRegistry->register('spinwheel_data', $rowData);

        // Prepare the result page
        $resultPage = $this->resultFactory->create(ResultFactory::TYPE_PAGE);
        $title = $rowId ? __('Edit Spin Wheel').$rowData->getTitle() : __('Add Spin Wheel');
      
        $resultPage->getConfig()->getTitle()->prepend($title);
        return $resultPage;
    }

    protected function _isAllowed()
    {
        return $this->_authorization->isAllowed('Coditron_Lixtask::spin_wheel');
    }
}
