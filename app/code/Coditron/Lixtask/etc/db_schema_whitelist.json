{"coditron_lixtask": {"column": {"id": true, "task_title": true, "project_id": true, "custom_currency_organisation_id": true, "reward_type": true, "coins_per_action": true, "approval_type": true, "proof_type": true, "task_method": true, "status": true, "number_of_submissions_allowed": true, "period_allowed_for_submissions": true, "image": true, "created_at": true}, "index": {"MAGELEARN_CUSTOMFORM_FIRST_NAME": true, "MAGELEARN_CUSTOMFORM_LAST_NAME": true, "MAGELEARN_CUSTOMFORM_EMAIL": true, "MAGELEARN_CUSTOMFORM_MESSAGE": true, "MAGELEARN_CUSTOMFORM_IMAGE": true}, "constraint": {"PRIMARY": true}}}