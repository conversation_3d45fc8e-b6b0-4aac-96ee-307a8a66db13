<?xml version="1.0" ?>
<schema xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:Setup/Declaration/Schema/etc/schema.xsd">
	<table name="coditron_lixtask" resource="default" engine="innodb" comment="Coditron Lix Task Table">
		<column xsi:type="smallint" name="id" padding="6" unsigned="true" nullable="false" identity="true" comment="ID"/>
		<constraint xsi:type="primary" referenceId="PRIMARY">
			<column name="id"/>
		</constraint>
		<column name="task_id" nullable="false" xsi:type="text" comment="Task ID"/>
		<column name="task_title" nullable="false" xsi:type="text" comment="Task Title"/>
		<column name="task_title_show" nullable="false" xsi:type="text" comment="Frontend Task Title"/>
		<column name="task_url" nullable="false" xsi:type="text" comment="Task Url"/>
		<column name="task_button_label" nullable="false" xsi:type="text" comment="Task Label"/>
		<column name="project_id" nullable="false" xsi:type="text" comment="Project Id"/>
		<column name="total_coins_available" nullable="false" xsi:type="int" comment="Total Coins Available"/>
		<column name="total_task_submission" nullable="false" xsi:type="int" comment="Total Task Submission"/>
		<column name="description" nullable="false" xsi:type="text" comment="Description"/>
		<column name="reward_expires_in" nullable="false" xsi:type="int" comment="Reward Expires In"/>
		<column name="is_staff_approval" nullable="false" xsi:type="text" comment="Is Staff Approval"/>
		<column name="shared_organisation_id" nullable="false" xsi:type="text" comment="Shared Organisation Id"/>
		<column name="number_of_submissions_allowed_per_period" nullable="false" xsi:type="text" comment="Number Of Submissions Allowed Per Period"/>
		<column name="custom_currency_organisation_id" nullable="false" xsi:type="text" comment="Custom Currency Organisation Id"/>
		<column name="reward_type" nullable="false" xsi:type="int" padding="10"  comment="Reward Type"/>
		<column name="coins_per_action" nullable="true" xsi:type="text" comment="Coins Per Action"/>
		<column name="xp_points" nullable="true" xsi:type="int" comment="XP Points Per Action"/>
		<column name="approval_type" padding="11" unsigned="false" nullable="false" xsi:type="int" default="1" identity="false" comment="Approval Type"/>
		<column name="proof_type" nullable="true" xsi:type="text"  comment="Proof Type"/>
		<column name="task_method" nullable="true" xsi:type="text" comment="Task Method"/>
		<column name="status" nullable="true" xsi:type="text"  comment="Status"/>
		<column name="number_of_submissions_allowed" nullable="true" xsi:type="text"  comment="Number Of Submissions Allowed"/>
		<column name="period_allowed_for_submissions" nullable="true" xsi:type="text" comment="Period Allowed For Submissions"/>
		<column name="image" nullable="true" xsi:type="text" comment="Image"/>
		<column name="created_at" nullable="false" xsi:type="datetime" comment="Created Date" default="CURRENT_TIMESTAMP"/>
		<!-- <index referenceId="MAGELEARN_CUSTOMFORM_FIRST_NAME" indexType="fulltext">
	        <column name="first_name"/>
	    </index>
	    <index referenceId="MAGELEARN_CUSTOMFORM_LAST_NAME" indexType="fulltext">
	        <column name="last_name"/>
	    </index>
	    <index referenceId="MAGELEARN_CUSTOMFORM_EMAIL" indexType="fulltext">
	        <column name="email"/>
	    </index>
	    <index referenceId="MAGELEARN_CUSTOMFORM_MESSAGE" indexType="fulltext">
	        <column name="message"/>
	    </index> -->
	    <index referenceId="MAGELEARN_CUSTOMFORM_IMAGE" indexType="fulltext">
	        <column name="image"/>
	    </index>
	</table>

	<table name="coditron_scrachcard" resource="default" engine="innodb" comment="Coditron scratch card Table">
		<column xsi:type="smallint" name="id" padding="6" unsigned="true" nullable="false" identity="true" comment="ID"/>
		<constraint xsi:type="primary" referenceId="PRIMARY">
			<column name="id"/>
		</constraint>
		<column name="task_id" nullable="false" xsi:type="text" comment="Task ID"/>
		<column name="project_id" nullable="false" xsi:type="int" comment="Project Id"/>
		<column name="title" nullable="false" xsi:type="text" comment="Task Title"/>
		<column name="custom_currency_organisation_id" nullable="false" xsi:type="int" comment="Custom Currency Organisation Id"/>
		<column name="total_coins_available" nullable="false" xsi:type="int" comment="Total Coins Available"/>
		<column name="reward_amounts" nullable="false" xsi:type="text" comment="Total Reward Amount"/>
		<column name="approval_type" nullable="false" xsi:type="text" comment="Approval Type"/>
		<column name="reward_expires_in" nullable="false" xsi:type="int" comment="Reward Expires In"/>
		<column name="has_location_trigger" nullable="false" xsi:type="boolean" comment="Has Location Trigger"/>
		<column name="location_id" nullable="false" xsi:type="text" comment="Location Id"/>
		<column name="schedule_local_time" nullable="false" xsi:type="datetime" comment="Schedule Local Time" default="CURRENT_TIMESTAMP"/>
		<column name="time_zone" nullable="false" xsi:type="text"   comment="Time Zone"/>
		<column name="status" nullable="true" xsi:type="text"  comment="Status"/>
		<column name="qr_code_url" nullable="false" xsi:type="text" comment="QR Code URL"/>
		<column name="description" nullable="true" xsi:type="text"  comment="Description"/>
		<column name="task_type" nullable="true" xsi:type="text"  comment="Task Type"/>
		<column name="link_url" nullable="true" xsi:type="text"  comment="Link Url"/>
		<column name="created_at" nullable="false" xsi:type="datetime" comment="Created Time" default="CURRENT_TIMESTAMP"/>
		<column name="updated_at" nullable="false" xsi:type="datetime" comment="Updated Time" default="CURRENT_TIMESTAMP"/>

	</table>

	<table name="coditron_spinwheel" resource="default" engine="innodb" comment="Coditron Spin the Wheel Table">
		<column xsi:type="smallint" name="id" padding="6" unsigned="true" nullable="false" identity="true" comment="ID"/>
		<constraint xsi:type="primary" referenceId="PRIMARY">
			<column name="id"/>
		</constraint>
		<column name="project_id" nullable="false" xsi:type="int" comment="Project Id"/>
		<column name="task_id" nullable="true" xsi:type="text"  comment="Task Id"/>
		<column name="title" nullable="false" xsi:type="text" comment="Task Title"/>
		<column name="custom_currency_organisation_id" nullable="false" xsi:type="text" comment="Custom Currency Organisation Id"/>
		<column name="total_coins_available" nullable="false" xsi:type="int" comment="Total Coins Available"/>
		<column name="reward_amounts" nullable="false" xsi:type="text" comment="Total Reward Amount"/>
		<column name="approval_type" nullable="false" xsi:type="text" comment="Approval Type"/>
		<column name="reward_expires_in" nullable="false" xsi:type="int" comment="Reward Expires In"/>
		<column name="has_location_trigger" nullable="false" xsi:type="boolean" comment="Has Location Trigger"/>
		<column name="location_id" nullable="false" xsi:type="int" comment="Location Id"/>
		<column name="schedule_local_time" nullable="false" xsi:type="datetime" comment="Schedule Local Time" default="CURRENT_TIMESTAMP"/>
		<column name="time_zone" nullable="false" xsi:type="text"   comment="Time Zone"/>
		<column name="status" nullable="true" xsi:type="text"  comment="Status"/>
		<column name="qr_code_url" nullable="false" xsi:type="text" comment="QR Code URL"/>
        <column name="description" nullable="true" xsi:type="text"  comment="Description"/>
		<column name="task_type" nullable="true" xsi:type="text"  comment="Task Type"/>
		<column name="link_url" nullable="true" xsi:type="text"  comment="Link Url"/>
		<column name="created_at" nullable="false" xsi:type="datetime" comment="Created Time" default="CURRENT_TIMESTAMP"/>
		<column name="updated_at" nullable="false" xsi:type="datetime" comment="Updated Time" default="CURRENT_TIMESTAMP"/>
	</table>

	<table name="coditron_predict" resource="default" engine="innodb" comment="Coditron Predict Task Table">
		<column xsi:type="smallint" name="id" padding="6" unsigned="true" nullable="false" identity="true" comment="ID"/>
		<constraint xsi:type="primary" referenceId="PRIMARY">
			<column name="id"/>
		</constraint>
		<column name="task_id" nullable="false" xsi:type="text" comment="Task ID"/>
		<column name="project_id" nullable="false" xsi:type="int" comment="Project Id"/>
		<column name="title" nullable="false" xsi:type="text" comment="Task Title"/>
		<column name="custom_currency_organisation_id" nullable="false" xsi:type="int" comment="Custom Currency Organisation Id"/>
		<column name="total_coins_available" nullable="false" xsi:type="int" comment="Total Coins Available"/>
		<column name="league_id" nullable="false" xsi:type="int" comment="League Id"/>
		<column name="fixture_id" nullable="false" xsi:type="int" comment="Fixture Id"/>

		<column name="team_home_id" nullable="false" xsi:type="int" comment="Team Home Id"/>
		<column name="team_home_name" nullable="false" xsi:type="text" comment="Team Home Name"/>
		<column name="team_home_logo" nullable="false" xsi:type="int" comment="Team Home Logo"/>
		<column name="team_away_id" nullable="false" xsi:type="int" comment="Team Away Id"/>
		<column name="team_away_name" nullable="false" xsi:type="text" comment="Team Away Name"/>
		<column name="team_away_logo" nullable="false" xsi:type="int" comment="Team Away Logo"/>
		<column name="match_date" nullable="false" xsi:type="int" comment="Match Date"/>
		<column name="league_name" nullable="false" xsi:type="text" comment="League Name"/>
		<column name="league_logo" nullable="false" xsi:type="int" comment="League Logo"/>

		<column name="reward_amounts" nullable="false" xsi:type="text" comment="Total Reward Amount"/>
		<column name="has_location_trigger" nullable="false" xsi:type="boolean" comment="Has Location Trigger"/>
		<column name="location_id" nullable="false" xsi:type="int" comment="Location Id"/>
		<column name="schedule_local_time" nullable="false" xsi:type="datetime" comment="Schedule Local Time" default="CURRENT_TIMESTAMP"/>
		<column name="time_zone" nullable="false" xsi:type="text"   comment="Time Zone"/>
		<column name="status" nullable="true" xsi:type="text"  comment="Status"/>
		<column name="qr_code_url" nullable="false" xsi:type="text" comment="QR Code URL"/>
	</table>

	<table name="quiz_task" resource="default" engine="innodb" comment="Quiz Project Table">
        <column xsi:type="int" name="task_id" nullable="false" unsigned="true" comment="Task ID"/>
        <column xsi:type="int" name="project_id" nullable="false" unsigned="true" comment="Project ID"/>
        <column xsi:type="int" name="quiz_id" nullable="false" identity="true" unsigned="true" comment="Quiz ID"/>
        <column xsi:type="int" name="custom_currency_organisation_id" nullable="false" comment="Custom Currency Organisation ID"/>
        <column xsi:type="varchar" name="title" nullable="false" length="255" comment="Title"/>
        <column xsi:type="int" name="total_coins_available" nullable="false" comment="Total Coins Available"/>
        <!-- <column xsi:type="decimal" name="reward_amount_per_question" nullable="false" scale="2" precision="10" comment="Reward Amount Per Question"/> -->
        <column xsi:type="int" name="reward_amount_per_question" nullable="false" comment="Reward Amount Per Question"/>
        <column xsi:type="varchar" name="approval_type" nullable="false" length="50" comment="Approval Type"/>
        <column xsi:type="int" name="reward_expires_in" nullable="false" comment="Reward Expires In"/>
        <column xsi:type="boolean" name="has_location_trigger" nullable="false" comment="Has Location Trigger"/>
        <!-- <column xsi:type="boolean" name="has_location_trigger" nullable="false" comment="Has Location Trigger"/> -->
        <column xsi:type="int" name="location_id" nullable="false" comment="Location ID"/>
        <column xsi:type="timestamp" name="quiz_date_schedule_local_time" nullable="false" default="CURRENT_TIMESTAMP" comment="Quiz Date Schedule Local Time"/>
        <column xsi:type="varchar" name="time_zone" nullable="false" length="255" comment="Time Zone"/>
        <!-- <column xsi:type="smallint" name="is_active" nullable="false" comment="Active Status"/> -->
		<column xsi:type="text" name="is_active" nullable="false" comment="Active Status"/>
        <constraint xsi:type="primary" referenceId="PRIMARY">
            <column name="quiz_id"/>
        </constraint>
        <!-- <constraint xsi:type="index" referenceId="QUIZ_TASK_PROJECT_ID_INDEX" indexType="btree">
            <column name="project_id"/>
        </constraint> -->
    </table>

    <table name="quiz_questions" resource="default" engine="innodb" comment="Quiz Questions Table">
        <column xsi:type="int" name="question_id" nullable="false" identity="true" unsigned="true" comment="Question ID"/>
        <column xsi:type="int" name="quiz_id" nullable="false" unsigned="true" comment="Quiz ID"/>
        <column xsi:type="text" name="question" nullable="false" comment="Question"/>
        <column xsi:type="text" name="quiz_image" nullable="true" comment="Quiz Image URL"/>
        <constraint xsi:type="primary" referenceId="PRIMARY">
            <column name="question_id"/>
        </constraint>
        <constraint xsi:type="foreign" referenceId="FK_QUIZ_QUIZ_ID" table="quiz_task" column="quiz_id" referenceTable="quiz_task" referenceColumn="quiz_id" onDelete="CASCADE"/>
    </table>

    <table name="quiz_answers" resource="default" engine="innodb" comment="Quiz Answers Table">
        <column xsi:type="int" name="answer_id" nullable="false" identity="true" unsigned="true" comment="Answer ID"/>
        <column xsi:type="int" name="question_id" nullable="false" unsigned="true" comment="Question ID"/>
        <column xsi:type="text" name="answer" nullable="false" comment="Answer"/>
        <!-- <column xsi:type="tinyint" name="is_correct" nullable="false" default="0" comment="Is Correct Answer"/> -->
        <column xsi:type="varchar" name="is_correct" nullable="false" comment="Is Correct Answer"/>
        <constraint xsi:type="primary" referenceId="PRIMARY">
            <column name="answer_id"/>
        </constraint>
        <constraint xsi:type="foreign" referenceId="FK_QUIZ_QUESTION_ID" table="quiz_questions" column="question_id" referenceTable="quiz_questions" referenceColumn="question_id" onDelete="CASCADE"/>
    </table>
</schema>
