<?xml version="1.0" ?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
	<preference for="Coditron\Lixtask\Api\CustomformRepositoryInterface"
                type="Coditron\Lixtask\Model\CustomformRepository"/>
	<preference for="Coditron\Lixtask\Api\Data\CustomformInterface"
                type="Coditron\Lixtask\Model\Data\Customform"/>
	<preference for="Coditron\Lixtask\Api\Data\CustomformSearchResultsInterface"
                type="Magento\Framework\Api\SearchResults"/>
	<preference for="Coditron\Lixtask\Api\Data\GridInterface"
                type="Coditron\Lixtask\Model\Grid" />
	<preference for="Coditron\Lixtask\Api\Data\ScrachcardInterface"
                type="Coditron\Lixtask\Model\Scrachcard" />
	<preference for="Coditron\Lixtask\Api\Data\SpinwheelInterface"
                type="Coditron\Lixtask\Model\Spinwheel" />
    <!-- @TODO: does not exits -->
<!--	<preference for="Coditron\Lixtask\Api\Data\PredictInterface" type="Coditron\Lixtask\Model\Predict" />-->

	<virtualType name="Coditron\Lixtask\Model\ResourceModel\Customform\Grid\Collection" type="Magento\Framework\View\Element\UiComponent\DataProvider\SearchResult">
		<arguments>
			<argument name="mainTable" xsi:type="string">coditron_lixtask</argument>
			<argument name="resourceModel" xsi:type="string">Coditron\Lixtask\Model\ResourceModel\Customform\Collection</argument>
		</arguments>
	</virtualType>
	<type name="Magento\Framework\View\Element\UiComponent\DataProvider\CollectionFactory">
		<arguments>
			<argument name="collections" xsi:type="array">
				<item name="coditron_lixtask_listing_data_source" xsi:type="string">Coditron\Lixtask\Model\ResourceModel\Customform\Grid\Collection</item>
			</argument>
		</arguments>
	</type>
	<virtualType name="Coditron\Lixtask\Model\ResourceModel\Grid\Grid\Collection" type="Magento\Framework\View\Element\UiComponent\DataProvider\SearchResult">
        <arguments>
            <argument name="mainTable" xsi:type="string">wk_grid_records</argument>
            <argument name="resourceModel" xsi:type="string">Coditron\Lixtask\Model\ResourceModel\Grid</argument>
        </arguments>
    </virtualType>
    <type name="Magento\Framework\View\Element\UiComponent\DataProvider\CollectionFactory">
        <arguments>
            <argument name="collections" xsi:type="array">
                <item name="cdadmincustomform_record_grid_list_data_source"  xsi:type="string">Coditron\Lixtask\Model\ResourceModel\Grid\Grid\Collection</item>
            </argument>
        </arguments>
    </type>

	<virtualType name="Coditron\Lixtask\Model\ResourceModel\Scrachcard\Grid\Collection" type="Magento\Framework\View\Element\UiComponent\DataProvider\SearchResult">
        <arguments>
            <argument name="mainTable" xsi:type="string">coditron_scrachcard</argument>
            <argument name="resourceModel" xsi:type="string">Coditron\Lixtask\Model\ResourceModel\Scrachcard</argument>
        </arguments>
    </virtualType>
    <type name="Magento\Framework\View\Element\UiComponent\DataProvider\CollectionFactory">
        <arguments>
            <argument name="collections" xsi:type="array">
                <item name="cdadmincustomform_scrachcard_list_data_source"  xsi:type="string">Coditron\Lixtask\Model\ResourceModel\Scrachcard\Grid\Collection</item>
            </argument>
        </arguments>
    </type>

    <virtualType name="Coditron\Lixtask\Model\ResourceModel\Spinwheel\Grid\Collection" type="Magento\Framework\View\Element\UiComponent\DataProvider\SearchResult">
        <arguments>
            <argument name="mainTable" xsi:type="string">coditron_spinwheel</argument>
            <argument name="resourceModel" xsi:type="string">Coditron\Lixtask\Model\ResourceModel\Spinwheel</argument>
        </arguments>
    </virtualType>
    <type name="Magento\Framework\View\Element\UiComponent\DataProvider\CollectionFactory">
        <arguments>
            <argument name="collections" xsi:type="array">
                <item name="cdadmincustomform_spinwheel_list_data_source"  xsi:type="string">Coditron\Lixtask\Model\ResourceModel\Spinwheel\Grid\Collection</item>
            </argument>
        </arguments>
    </type>

	<virtualType name="Coditron\Lixtask\Model\ResourceModel\Predict\Grid\Collection" type="Magento\Framework\View\Element\UiComponent\DataProvider\SearchResult">
        <arguments>
            <argument name="mainTable" xsi:type="string">coditron_predict</argument>
            <argument name="resourceModel" xsi:type="string">Coditron\Lixtask\Model\ResourceModel\Predict</argument>
        </arguments>
    </virtualType>
    <type name="Magento\Framework\View\Element\UiComponent\DataProvider\CollectionFactory">
        <arguments>
            <argument name="collections" xsi:type="array">
                <item name="cdadmincustomform_predict_list_data_source"  xsi:type="string">Coditron\Lixtask\Model\ResourceModel\Predict\Grid\Collection</item>
            </argument>
        </arguments>
    </type>

    		<virtualType name="Coditron\Lixtask\Model\ResourceModel\Quiz\Grid\Collection" type="Magento\Framework\View\Element\UiComponent\DataProvider\SearchResult">
        <arguments>
        	<!-- Table name to insert data and listing data -->
            <argument name="mainTable" xsi:type="string">quiz_task</argument>
            <!-- Resource model name -->
            <argument name="resourceModel" xsi:type="string">Coditron\Lixtask\Model\ResourceModel\Quiz</argument>
        </arguments>
    </virtualType>
	<type name="Magento\Framework\View\Element\UiComponent\DataProvider\CollectionFactory">
        <arguments>
            <argument name="collections" xsi:type="array">
            	<!-- Data Source will be mentioned in listing xml -->
                <item name="cdadmincustomform_record_quiz_list_data_source" xsi:type="string">Coditron\Lixtask\Model\ResourceModel\Quiz\Grid\Collection</item>
            </argument>
        </arguments>
    </type>

	<virtualType name="Coditron\Lixtask\Model\ResourceModel\Questions\Grid\Collection" type="Magento\Framework\View\Element\UiComponent\DataProvider\SearchResult">
        <arguments>
        	<!-- Table name to insert data and listing data -->
            <argument name="mainTable" xsi:type="string">quiz_questions</argument>
            <!-- Resource model name -->
            <argument name="resourceModel" xsi:type="string">Coditron\Lixtask\Model\ResourceModel\Questions</argument>
        </arguments>
    </virtualType>
	<type name="Magento\Framework\View\Element\UiComponent\DataProvider\CollectionFactory">
        <arguments>
            <argument name="collections" xsi:type="array">
            	<!-- Data Source will be mentioned in listing xml -->
                <item name="cdadmincustomform_record_question_list_data_source" xsi:type="string">Coditron\Lixtask\Model\ResourceModel\Questions\Grid\Collection</item>
            </argument>
        </arguments>
    </type>

	<virtualType name="Coditron\Lixtask\Model\ResourceModel\Answers\Grid\Collection" type="Magento\Framework\View\Element\UiComponent\DataProvider\SearchResult">
        <arguments>
        	<!-- Table name to insert data and listing data -->
            <argument name="mainTable" xsi:type="string">quiz_answers</argument>
            <!-- Resource model name -->
            <argument name="resourceModel" xsi:type="string">Coditron\Lixtask\Model\ResourceModel\Answers</argument>
        </arguments>
    </virtualType>
	<type name="Magento\Framework\View\Element\UiComponent\DataProvider\CollectionFactory">
        <arguments>
            <argument name="collections" xsi:type="array">
            	<!-- Data Source will be mentioned in listing xml -->
                <item name="cdadmincustomform_record_answer_list_data_source" xsi:type="string">Coditron\Lixtask\Model\ResourceModel\Answers\Grid\Collection</item>
            </argument>
        </arguments>
    </type>

	<virtualType name="Coditron\Lixtask\CustomformImageUpload" type="Coditron\Lixtask\Model\ImageUploader">
	    <arguments>
	            <argument name="baseTmpPath" xsi:type="string">coditron/customform/tmp</argument>
	            <argument name="basePath" xsi:type="string">coditron/customform</argument>
	            <argument name="allowedExtensions" xsi:type="array">
	                <item name="jpg" xsi:type="string">jpg</item>
	                <item name="jpeg" xsi:type="string">jpeg</item>
	                <item name="gif" xsi:type="string">gif</item>
	                <item name="png" xsi:type="string">png</item>
	            </argument>
	    </arguments>
	</virtualType>

	<type name="Coditron\Lixtask\Controller\Adminhtml\Customform\FileUploader\Save">
	    <arguments>
	            <argument name="imageUploader" xsi:type="object">Coditron\Lixtask\CustomformImageUpload</argument>
	    </arguments>
	</type>
</config>
