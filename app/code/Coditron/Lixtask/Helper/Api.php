<?php
declare(strict_types=1);

namespace Coditron\Lixtask\Helper;

use Magento\Framework\App\Helper\AbstractHelper;
use Magento\Framework\App\Helper\Context;
use Magento\Store\Model\ScopeInterface;
use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Customer\Model\CustomerFactory;
use Magento\Customer\Model\ResourceModel\Customer;


class Api extends AbstractHelper
{
    const URL_PATH_KEY = 'sleekaccordian/general/odata_base_url';
    const URL_USER_KEY = 'sleekaccordian/general/user';
    const URL_ACCESS_KEY = 'sleekaccordian/general/access_key';
    const URL_SITE_KEY = 'sleekaccordian/general/site_url';

    const URL_ORGANISATION_KEY = 'sleekaccordian/general/organisation_id';
    const URL_PROJECT_KEY = 'sleekaccordian/general/project_id';
    const URL_CURRENCYORG_KEY = 'sleekaccordian/general/currency_organisation_id';
    const URL_TASKID_KEY = 'sleekaccordian/general/task_id';

    const URL_ITEM_REWARD = 'sleekaccordian/general/order_item_reward';
    const URL_ITEM_REWARD_SECOND = 'sleekaccordian/general/order_item_reward_second';
    const URL_REWARD_TYPE = 'sleekaccordian/general/order_reward_type';
    const URL_ITEMCOST_TYPE = 'sleekaccordian/general/order_item_cost';
    const URL_REWARDID = 'sleekaccordian/general/reward_id';
    const URL_REWARD_CURRENCY = 'sleekaccordian/general/reward_currency';
    const URL_LIXX_TASK_NAME = 'sleekaccordian/general/lixx_task_name';


    const URL_LIXPP_POINT = 'lix_general/cashpoint/cash_point';
    const URL_LIXPP_CURRENCY = 'lix_general/cashpoint/cash_currency';
    const URL_LIXX_CURRENCY = 'lix_general/lixxcurrencypoint/currency_lixx';
    const URL_LIXX_POINT = 'lix_general/lixxcurrencypoint/point_lixx';
    const URL_LIX_POINT = 'lix_general/lixcurrencypoint/point_lix';
    const URL_LIX_CURRENCY = 'lix_general/lixcurrencypoint/currency_lix';

    const URL_DEFAULT_OPTION = 'lix_general/general/select';

    const URL_FIRST_LIX_POINT = 'sleekaccordian/general/lix_task_id';
    const URL_EVERY_LIX_POINT = 'sleekaccordian/general/every_lix_task_id';
    const URL_FIRST_CASH_POINT = 'sleekaccordian/general/cashpoint_task_id';
    const URL_EVERY_CASH_POINT = 'sleekaccordian/general/every_cash_task_id';
    const URL_NEWS_POINT = 'sleekaccordian/general/news_id';
    const URL_NEWS_LIX_POINT = 'sleekaccordian/general/news_lix_id';

    const URL_LIXPP_WALLET_NAME = 'lix_general/cashpoint/lixpp_wallet_name';
    const URL_LIXPP_CUSTOM_CURRENCY_ORG_ID = 'lix_general/cashpoint/lixpp_custom_currency_organisation_id';
    const URL_LIXX_WALLET_NAME = 'lix_general/lixxcurrencypoint/lixx_wallet_name';
    const URL_LIXX_CUSTOM_CURRENCY_ORG_ID = 'lix_general/lixxcurrencypoint/lixx_custom_currency_organisation_id';
    const URL_LIX_WALLET_NAME = 'lix_general/lixcurrencypoint/lix_wallet_name';
    const URL_LIX_CUSTOM_CURRENCY_ORG_ID = 'lix_general/lixcurrencypoint/lix_custom_currency_organisation_id';


    public function __construct(
        Context $context,
        private CustomerFactory $customerFactory,
    )
    {
        parent::__construct($context);
    }

    public function getConfigValue($field, $storeId = null)
    {
        return $this->scopeConfig->getValue(
            $field, ScopeInterface::SCOPE_STORE, $storeId
        );
    }

    public function getSiteUrl($storeId = null)
    {
        return $this->getConfigValue(self::URL_SITE_KEY, $storeId);
    }

    public function getToken($storeId = null)
    {
        return $this->getConfigValue(self::URL_ACCESS_KEY, $storeId);
    }

    public function getOrganisationId($storeId = null)
    {
        return $this->getConfigValue(self::URL_ORGANISATION_KEY, $storeId);
    }

    public function getProjectId($storeId = null)
    {
        return $this->getConfigValue(self::URL_PROJECT_KEY, $storeId);
    }

    public function getCurrencyOrgId($storeId = null)
    {
        return $this->getConfigValue(self::URL_CURRENCYORG_KEY, $storeId);
    }

    public function getTaskId($storeId = null)
    {
        return $this->getConfigValue(self::URL_TASKID_KEY, $storeId);
    }

    public function getItemReward($storeId = null)
    {
        return $this->getConfigValue(self::URL_ITEM_REWARD, $storeId);
    }

    public function getRewardType($storeId = null)
    {
        return $this->getConfigValue(self::URL_REWARD_TYPE, $storeId);
    }

    public function getRewardId($storeId = null)
    {
        return $this->getConfigValue(self::URL_REWARDID, $storeId);
    }

    public function getRewardCurrency($storeId = null)
    {
        return $this->getConfigValue(self::URL_REWARD_CURRENCY, $storeId);
    }

    public function getLixxTaskName($storeId = null)
    {
        return $this->getConfigValue(self::URL_LIXX_TASK_NAME, $storeId);
    }

    public function getItemCost($storeId = null)
    {
        return $this->getConfigValue(self::URL_ITEMCOST_TYPE, $storeId);
    }

    public function getItemRewardSecond($storeId = null)
    {
        return $this->getConfigValue(self::URL_ITEM_REWARD_SECOND, $storeId);
    }

    public function getCashPointValues($storeId = null)
    {
        return $this->getConfigValue(self::URL_FIRST_CASH_POINT, $storeId);
    }

    public function getCashCurrenciesValues($storeId = null)
    {
        return $this->getConfigValue(self::URL_LIXPP_CURRENCY, $storeId);
    }

    public function getLixxCurrenciesValues($storeId = null)
    {
        return $this->getConfigValue(self::URL_LIXX_CURRENCY, $storeId);
    }

    public function getLixPointValues($storeId = null)
    {
        return $this->getConfigValue(self::URL_LIX_POINT, $storeId);
    }

    public function getLixPPWalletName($storeId = null)
    {
        return $this->getConfigValue(self::URL_LIXPP_WALLET_NAME, $storeId);
    }

    public function getLixPPCustomORGId($storeId = null)
    {
        return $this->getConfigValue(self::URL_LIXPP_CUSTOM_CURRENCY_ORG_ID, $storeId);
    }

    public function getLixxWalletName($storeId = null)
    {
        return $this->getConfigValue(self::URL_LIXX_WALLET_NAME, $storeId);
    }

    public function getLixxCustomORGId($storeId = null)
    {
        return $this->getConfigValue(self::URL_LIXX_CUSTOM_CURRENCY_ORG_ID, $storeId);
    }

    public function getLixWalletName($storeId = null)
    {
        return $this->getConfigValue(self::URL_LIX_WALLET_NAME, $storeId);
    }

    public function getLixCustomORGId($storeId = null)
    {
        return $this->getConfigValue(self::URL_LIX_CUSTOM_CURRENCY_ORG_ID, $storeId);
    }

    public function getLixCurrenciesValues($cemail, $attributeCode, $storeId = null)
    {

        $customer = $this->customerFactory->create();
        $customer->setWebsiteId(1);
        $customer->loadByEmail($cemail);
        $testAttr = $customer->getData($attributeCode);

        if ($testAttr == 'LIXX') {
            return $this->getConfigValue(self::URL_LIXX_CURRENCY);
        } else {
            return $this->getConfigValue(self::URL_LIXPP_CURRENCY);
        }
    }

    public function getDefaultPointOption($storeId = null)
    {
        return $this->scopeConfig->getValue('lix_general/general/select', \Magento\Store\Model\ScopeInterface::SCOPE_STORE);
    }

    public function getFirstLixTaskId($storeId = null)
    {
        return $this->getConfigValue(self::URL_FIRST_LIX_POINT, $storeId);
    }

    public function getEveryLixTaskId($storeId = null)
    {
        return $this->getConfigValue(self::URL_EVERY_LIX_POINT, $storeId);
    }

    public function getFirstCashTaskId($storeId = null)
    {
        return $this->getConfigValue(self::URL_FIRST_CASH_POINT, $storeId);
    }

    public function getEveryCashTaskId($storeId = null)
    {
        return $this->getConfigValue(self::URL_EVERY_CASH_POINT, $storeId);
    }

    public function getNewsLetterTaskId($storeId = null)
    {
        return $this->getConfigValue(self::URL_NEWS_POINT, $storeId);
    }

    public function getNewsLetterLixTaskId($storeId = null)
    {
        return $this->getConfigValue(self::URL_NEWS_LIX_POINT, $storeId);
    }


    public function setLixCustomers($custData)
    {
        $url = $this->getSiteUrl();
        $access_token = $this->getToken();
        $authorization = "Developer-Token: " . $access_token;

        try {
            $ch = curl_init();

            curl_setopt($ch, CURLOPT_URL, $url . "user/register");
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $custData);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Accept: application/json',
                'Content-Type: application/json; charset=utf-8',
                $authorization
            ]);

            $response = json_decode(curl_exec($ch), TRUE);

        } catch (\Exception $e) {
            $this->messageManager->addErrorMessage($e->getMessage());
        }
        return $response;
    }

    public function getLixUserEmail($lemail)
    {
        $url = $this->getSiteUrl();
        $access_token = $this->getToken();
        $authorization = "Developer-Token: " . $access_token;

        $postfields = array();
        $postfields['email'] = $lemail;
        $postData = json_encode($postfields);

        try {
            $ch = curl_init();

            curl_setopt($ch, CURLOPT_URL, $url . "user/email/verify");
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Accept: application/json',
                'Content-Type: application/json; charset=utf-8',
                $authorization
            ]);
            $response = json_decode(curl_exec($ch), TRUE);

        } catch (\Exception $e) {
            $this->messageManager->addErrorMessage($e->getMessage());
        }
        return $response;
    }

    public function getLixOrganisationList($lixid)
    {
        $url = $this->getSiteUrl();
        $access_token = $this->getToken();
        $organisation_id = $this->getOrganisationId();

        $developerToken = "Developer-Token:" . $access_token;
        $authorization = "Authorization: Bearer " . $access_token;

        try {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url . "organisations/" . $lixid . "/show");
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_ENCODING, '');
            curl_setopt($ch, CURLOPT_MAXREDIRS, 10);
            curl_setopt($ch, CURLOPT_TIMEOUT, false);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
            curl_setopt($ch, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_1);
            curl_setopt($ch, CURLOPT_POST, false);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Accept: application/json',
                'Content-Type: application/json; charset=utf-8',
                $developerToken,
                $authorization
            ]);
            $response = json_decode(curl_exec($ch), true);

        } catch (\Exception $e) {
            return false;
        }
        return $response;
    }

    public function getLixProjectList($lproid)
    {
        $url = $this->getSiteUrl();
        $access_token = $this->getToken();
        $project_id = $this->getProjectId();

        $developerToken = "Developer-Token:" . $access_token;
        $authorization = "Authorization: Bearer " . $access_token;

        try {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url . "projects/" . $lproid);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_ENCODING, '');
            curl_setopt($ch, CURLOPT_MAXREDIRS, 10);
            curl_setopt($ch, CURLOPT_TIMEOUT, false);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
            curl_setopt($ch, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_1);
            curl_setopt($ch, CURLOPT_POST, false);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Accept: application/json',
                'Content-Type: application/json; charset=utf-8',
                $developerToken,
                $authorization
            ]);
            $response = json_decode(curl_exec($ch), true);

        } catch (\Exception $e) {
            return false;
        }
        return $response;
    }

    public function getAllLixTaskList($lproid, $nxtUrl)
    {
        $url = $this->getSiteUrl();
        $access_token = $this->getToken();

        $finalArray = array();
        $project_id = $lproid;
        //$project_id = $this->getProjectId();
        $developerToken = "Developer-Token:" . $access_token;
        $authorization = "Authorization: Bearer " . $access_token;

        try {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $nxtUrl);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_ENCODING, '');
            curl_setopt($ch, CURLOPT_MAXREDIRS, 10);
            curl_setopt($ch, CURLOPT_TIMEOUT, false);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
            curl_setopt($ch, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_1);
            curl_setopt($ch, CURLOPT_POST, false);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Accept: application/json',
                'Content-Type: application/json; charset=utf-8',
                $developerToken,
                $authorization
            ]);
            $response = json_decode(curl_exec($ch), true);
            foreach ($response['data']['data'] as $value) {
                $taskData = array();
                $taskData['id'] = $value['id'];
                $taskData['title'] = $value['title'];
                $taskData['description'] = $value['description'];
                $taskData['currency'] = $value['currency'];
                $taskData['reward_type'] = $value['reward_type'];
                $taskData['approval_type'] = $value['approval_type'];
                $taskData['proof_type'] = $value['proof_type'];
                $taskData['created_at'] = $value['created_at'];
                $taskData['coins_per_action'] = $value['coins_per_action'];
                $taskData['total_task_submission'] = $value['total_task_submission'];
                $taskData['status'] = $value['status'];
                $taskData['task_method'] = $value['task_method'];
                $taskData['shared_organisation_id'] = $value['shared_organisation_id'];
                $taskData['is_staff_approval'] = $value['is_staff_approval'];
                $taskData['number_of_submissions_allowed'] = $value['number_of_submissions_allowed'];
                $taskData['number_of_submissions_allowed_per_period'] = $value['number_of_submissions_allowed_per_period'];
                //todo clarify organisation_id
                $taskData['custom_currency_organisation_id'] = 0;
                foreach ($value['reward_configuration'] as $taskid) {
                    $taskData['custom_currency_organisation_id'] = $taskid['custom_currency_organisation_id'];
                }
                $finalArray[] = $taskData;
            }

        } catch (\Exception $e) {
            echo 'Caught exception: ', $e->getMessage(), "\n";
            return false;
        }

        return $finalArray;
    }

    public function getLixTaskList($ltaskid)
    {
        $url = $this->getSiteUrl();
        $access_token = $this->getToken();
        $task_id = $this->getTaskId();

        $developerToken = "Developer-Token:" . $access_token;
        $authorization = "Authorization: Bearer " . $access_token;

        try {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url . "tasks/" . $ltaskid . "/show");

            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_ENCODING, '');
            curl_setopt($ch, CURLOPT_MAXREDIRS, 10);
            curl_setopt($ch, CURLOPT_TIMEOUT, false);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
            curl_setopt($ch, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_1);
            curl_setopt($ch, CURLOPT_POST, false);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Accept: application/json',
                'Content-Type: application/json; charset=utf-8',
                $developerToken,
                $authorization
            ]);
            $response = json_decode(curl_exec($ch), true);

        } catch (\Exception $e) {
            return false;
        }
        return $response;
    }

    public function getLixReward($ltaskid, $rewardData, $rewardValue)
    {
        $url = $this->getSiteUrl();
        $access_token = $this->getToken();
        $task_id = $this->getTaskId();
        $itemReward = $this->getItemReward();
        $rewardId = $this->getRewardId();

        $developerToken = "Developer-Token:" . $access_token;
        $authorization = "Authorization: Bearer " . $access_token;

        try {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url . "task/" . $ltaskid . "/reward/configuration");

            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $rewardData);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Accept: application/json',
                'Content-Type: application/json; charset=utf-8',
                $developerToken,
                $authorization
            ]);
            $response = json_decode(curl_exec($ch), true);

        } catch (\Exception $e) {
            return false;
        }
        return $response;
    }

    public function setLixTask($apiData)
    {
        $url = $this->getSiteUrl();
        $access_token = $this->getToken();
        $task_id = $this->getTaskId();

        $postData = json_encode($apiData);

        $developerToken = "Developer-Token:" . $access_token;
        $authorization = "Authorization: Bearer " . $access_token;

        try {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url . "tasks/create_new");

            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Accept: application/json',
                'Content-Type: application/json; charset=utf-8',
                $developerToken,
                $authorization
            ]);

            $response = json_decode(curl_exec($ch), true);

        } catch (\Exception $e) {
            return false;
        }
        return $response;
    }

    public function updateLixTask($taskid, $apiData)
    {
        $url = $this->getSiteUrl();
        $access_token = $this->getToken();

        $postData = json_encode($apiData);

        $developerToken = "Developer-Token:" . $access_token;
        $authorization = "Authorization: Bearer " . $access_token;

        try {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url . "tasks/" . $taskid . "/update");

            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Accept: application/json',
                'Content-Type: application/json; charset=utf-8',
                $developerToken,
                $authorization
            ]);
            $response = json_decode(curl_exec($ch), true);
        } catch (\Exception $e) {
            return false;
        }
        return $response;
    }

    public function createScratchCard($apiData)
    {
        $url = $this->getSiteUrl();
        $access_token = $this->getToken();
        $task_id = $this->getTaskId();


        $postData = json_encode($apiData);

        $developerToken = "Developer-Token:" . $access_token;
        $authorization = "Authorization: Bearer " . $access_token;

        try {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url . "tasks/scratch_card_task/create");

            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Accept: application/json',
                'Content-Type: application/json; charset=utf-8',
                $developerToken,
                $authorization
            ]);

            $response = json_decode(curl_exec($ch), true);


        } catch (\Exception $e) {
            return false;
        }
        return $response;
    }

    public function createSpinWheel($apiData)
    {
        $url = $this->getSiteUrl();
        $access_token = $this->getToken();
        $task_id = $this->getTaskId();

        $postData = json_encode($apiData);

        $developerToken = "Developer-Token:" . $access_token;
        $authorization = "Authorization: Bearer " . $access_token;

        try {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url . "tasks/spin_the_wheel_task/create");

            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Accept: application/json',
                'Content-Type: application/json; charset=utf-8',
                $developerToken,
                $authorization
            ]);

            $response = json_decode(curl_exec($ch), true);
        } catch (\Exception $e) {
            return false;
        }
        return $response;
    }

    public function getWalletName()
    {
        $result = array();
        $lixppwn = $this->getLixPPWalletName();
        $lixppci = $this->getLixPPCustomORGId();
        $lixwn = $this->getLixWalletName();
        $lixci = $this->getLixCustomORGId();
        $lixxwn = $this->getLixxWalletName();
        $lixxci = $this->getLixxCustomORGId();

        $result[] = array("label" => $lixppwn, "value" => $lixppci);
        // $result[] = array("key" => $lixppwn, "value" => $lixppci);
        $result[] = array("label" => $lixxwn, "value" => $lixxci);

        return $result;
    }

    public function getAllSpinwheelTask($orgid)
    {
        $url = $this->getSiteUrl();
        $access_token = $this->getToken();

        $developerToken = "Developer-Token: " . $access_token;
        $authorization = "Authorization: Bearer " . $access_token;

        try {
            $ch = curl_init();
            // Adjust the URL as per the provided format
            $apiUrl = $url . "tasks/spin-the-wheel-tasks/{$orgid}?organisation_id={$orgid}";
            curl_setopt($ch, CURLOPT_URL, $apiUrl);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_ENCODING, '');
            curl_setopt($ch, CURLOPT_MAXREDIRS, 10);
            curl_setopt($ch, CURLOPT_TIMEOUT, false);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
            curl_setopt($ch, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_1);
            curl_setopt($ch, CURLOPT_HTTPGET, true);  // Use HTTP GET method
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Accept: application/json',
                'Content-Type: application/json; charset=utf-8',
                $developerToken,
                $authorization
            ]);

            $response = json_decode(curl_exec($ch), true);
            if (curl_errno($ch)) {
                throw new \Exception(curl_error($ch));
            }
            curl_close($ch);
        } catch (\Exception $e) {
            // Log or handle the exception as needed
            return false;
        }
        return $response;
    }

    public function getAllScrachTaskList($orgid)
    {
        $url = $this->getSiteUrl();
        $access_token = $this->getToken();

        $writer = new \Zend_Log_Writer_Stream(BP . '/var/log/quizlist.log');
        $logger = new \Zend_Log();
        $logger->addWriter($writer);

        $developerToken = "Developer-Token: " . $access_token;
        $authorization = "Authorization: Bearer " . $access_token;

        try {
            $ch = curl_init();
            // Adjust the URL as per the provided format
            $apiUrl = $url . "tasks/scratch-card-tasks/{$orgid}?organisation_id={$orgid}";
            curl_setopt($ch, CURLOPT_URL, $apiUrl);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_ENCODING, '');
            curl_setopt($ch, CURLOPT_MAXREDIRS, 10);
            curl_setopt($ch, CURLOPT_TIMEOUT, false);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
            curl_setopt($ch, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_1);
            curl_setopt($ch, CURLOPT_HTTPGET, true);  // Use HTTP GET method
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Accept: application/json',
                'Content-Type: application/json; charset=utf-8',
                $developerToken,
                $authorization
            ]);

            $response = json_decode(curl_exec($ch), true);
            // $logger->info("Final Task Data: " . print_r($response, true));
            if (curl_errno($ch)) {
                throw new \Exception(curl_error($ch));
            }
            curl_close($ch);
        } catch (\Exception $e) {
            // Log or handle the exception as needed
            return false;
        }
        return $response;
    }


    public function createQuizTask($data)
    {
        $url = $this->getSiteUrl();
        $access_token = $this->getToken();
        $task_id = $this->getTaskId();
        $postData = json_encode($data);

        $developerToken = "Developer-Token: " . $access_token;
        $authorization = "Authorization: Bearer " . $access_token;


        try {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url . "tasks/quiz_task/create");
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Accept: application/json',
                'Content-Type: application/json; charset=utf-8',
                $developerToken,
                $authorization
            ]);

            $response = json_decode(curl_exec($ch), true);

            if (curl_errno($ch)) {
                throw new \Exception(curl_error($ch));
            }
            curl_close($ch);

        } catch (\Exception $e) {
            $this->messageManager->addErrorMessage($e->getMessage());
            return false;
        }

        return $response;
    }

    public function getAllQuizTaskList($orgid)
    {
        $url = $this->getSiteUrl();
        $access_token = $this->getToken();

        $developerToken = "Developer-Token: " . $access_token;
        $authorization = "Authorization: Bearer " . $access_token;

        try {
            $ch = curl_init();
            // Adjust the URL as per the provided format
            $apiUrl = $url . "tasks/quiz-tasks/{$orgid}?organisation_id={$orgid}";
            curl_setopt($ch, CURLOPT_URL, $apiUrl);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_ENCODING, '');
            curl_setopt($ch, CURLOPT_MAXREDIRS, 10);
            curl_setopt($ch, CURLOPT_TIMEOUT, false);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
            curl_setopt($ch, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_1);
            curl_setopt($ch, CURLOPT_HTTPGET, true);  // Use HTTP GET method
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Accept: application/json',
                'Content-Type: application/json; charset=utf-8',
                $developerToken,
                $authorization
            ]);

            $response = json_decode(curl_exec($ch), true);
            if (curl_errno($ch)) {
                throw new \Exception(curl_error($ch));
            }
            curl_close($ch);
        } catch (\Exception $e) {
            // Log or handle the exception as needed
            return false;
        }
        return $response;
    }

    public function createPredictTask($apiData)
    {
        $url = $this->getSiteUrl();
        $access_token = $this->getToken();
        $task_id = $this->getTaskId();
        $postData = json_encode($apiData);

        $developerToken = "Developer-Token:" . $access_token;
        $authorization = "Authorization: Bearer " . $access_token;
        try {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url . "predictor-task/store");

            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Accept: application/json',
                'Content-Type: application/json; charset=utf-8',
                $developerToken,
                $authorization
            ]);
            $response = json_decode(curl_exec($ch), true);

        } catch (\Exception $e) {
            return false;
        }
        return $response;
    }


}
