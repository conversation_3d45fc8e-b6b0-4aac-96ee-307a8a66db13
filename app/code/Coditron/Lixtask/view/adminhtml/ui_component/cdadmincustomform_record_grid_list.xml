<?xml version="1.0" encoding="UTF-8"?>
<!--
/**
 * Grid record list UI Component
 * @category  Webkul
 * @package   Webkul_Grid
 * <AUTHOR>
 * @copyright Copyright (c) 2010-2017 Webkul Software Private Limited (https://webkul.com)
 * @license   https://store.webkul.com/license.html
 */
-->
<listing xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:Ui/etc/ui_configuration.xsd">
    <argument name="data" xsi:type="array">
        <item name="js_config" xsi:type="array">
            <item name="provider" xsi:type="string">cdadmincustomform_record_grid_list.cdadmincustomform_record_grid_list_data_source</item>
            <item name="deps" xsi:type="string">cdadmincustomform_record_grid_list.cdadmincustomform_record_grid_list_data_source</item>
        </item>
        <item name="spinner" xsi:type="string">grid_records_columns</item>
        <item name="buttons" xsi:type="array">
            <item name="add" xsi:type="array">
                <item name="name" xsi:type="string">add</item>
                <item name="label" xsi:type="string" translate="true">Add New Reward</item>
                <item name="class" xsi:type="string">primary</item>
                <item name="url" xsi:type="string">*/*/addrow</item>
            </item>
        </item>
    </argument>
    <dataSource name="grid_record_grid_list_data_source">
        <argument name="dataProvider" xsi:type="configurableObject">
            <argument name="class" xsi:type="string">Magento\Framework\View\Element\UiComponent\DataProvider\DataProvider</argument>
            <argument name="name" xsi:type="string">cdadmincustomform_record_grid_list_data_source</argument>
            <argument name="primaryFieldName" xsi:type="string">entity_id</argument>
            <argument name="requestFieldName" xsi:type="string">id</argument>
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="update_url" xsi:type="url" path="mui/index/render"/>
                </item>
            </argument>
        </argument>
        <argument name="data" xsi:type="array">
            <item name="js_config" xsi:type="array">
                <item name="component" xsi:type="string">Magento_Ui/js/grid/provider</item>
            </item>
        </argument>
    </dataSource>
    <container name="listing_top">
        <argument name="data" xsi:type="array">
            <item name="config" xsi:type="array">
                <item name="template" xsi:type="string">ui/grid/toolbar</item>
                <item name="stickyTmpl" xsi:type="string">ui/grid/sticky/toolbar</item>
            </item>
        </argument>
        <bookmark name="bookmarks"/>
        <columnsControls name="columns_controls"/>
        <filters name="listing_filters">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="columnsProvider" xsi:type="string">cdadmincustomform_record_grid_list.cdadmincustomform_record_grid_list.grid_records_columns</item>
                    <item name="storageConfig" xsi:type="array">
                        <item name="provider" xsi:type="string">cdadmincustomform_record_grid_list.cdadmincustomform_record_grid_list.listing_top.bookmarks</item>
                        <item name="namespace" xsi:type="string">current.filters</item>
                    </item>
                    <item name="templates" xsi:type="array">
                        <item name="filters" xsi:type="array">
                            <item name="select" xsi:type="array">
                                <item name="component" xsi:type="string">Magento_Ui/js/form/element/ui-select</item>
                                <item name="template" xsi:type="string">ui/grid/filters/elements/ui-select</item>
                            </item>
                        </item>
                    </item>
                    <item name="childDefaults" xsi:type="array">
                        <item name="provider" xsi:type="string">cdadmincustomform_record_grid_list.cdadmincustomform_record_grid_list.listing_top.listing_filters</item>
                        <item name="imports" xsi:type="array">
                            <item name="visible" xsi:type="string">cdadmincustomform_record_grid_list.cdadmincustomform_record_grid_list.grid_records_columns.${ $.index }:visible</item>
                        </item>
                    </item>
                </item>
                <item name="observers" xsi:type="array">
                    <item name="column" xsi:type="string">column</item>
                </item>
            </argument>
        </filters>
        <massaction name="listing_massaction">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="selectProvider" xsi:type="string">cdadmincustomform_record_grid_list.cdadmincustomform_record_grid_list.grid_records_columns.ids</item>
                    <item name="component" xsi:type="string">Magento_Ui/js/grid/tree-massactions</item>
                    <item name="indexField" xsi:type="string">id</item>
                </item>
            </argument>
            <!-- Mass actions which you want to add in your grid-->
            <action name="delete">
                <argument name="data" xsi:type="array">
                    <item name="config" xsi:type="array">
                        <item name="type" xsi:type="string">delete</item>
                        <item name="label" xsi:type="string" translate="true">Delete</item>
                        <item name="url" xsi:type="url" path="cdadmincustomform/grid/massdelete"/>
                        <item name="confirm" xsi:type="array">
                            <item name="title" xsi:type="string" translate="true">Delete</item>
                            <item name="message" xsi:type="string" translate="true">Do you want to delete selected row record?</item>
                        </item>
                    </item>
                </argument>
            </action>
            <action name="status">
                <settings>
                    <type>status</type>
                    <label translate="true">Change status</label>
                    <actions>
                        <action name="0">
                            <type>enable</type>
                            <label translate="true">Enable</label>
                            <url path="cdadmincustomform/grid/massenable">
                                <param name="status">1</param>
                            </url>
                        </action>
                        <action name="1">
                            <type>disable</type>
                            <label translate="true">Disable</label>
                            <url path="cdadmincustomform/grid/massdisable">
                                <param name="status">0</param>
                            </url>
                        </action>
                    </actions>
                </settings>
            </action>
        </massaction>
        <paging name="listing_paging">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="storageConfig" xsi:type="array">
                        <item name="provider" xsi:type="string">cdadmincustomform_record_grid_list.cdadmincustomform_record_grid_list.listing_top.bookmarks</item>
                        <item name="namespace" xsi:type="string">current.paging</item>
                    </item>
                    <item name="selectProvider" xsi:type="string">cdadmincustomform_record_grid_list.cdadmincustomform_record_grid_list.grid_records_columns.ids</item>
                </item>
            </argument>
        </paging>
    </container>
    <columns name="grid_records_columns">
        <selectionsColumn name="ids">
           <argument name="data" xsi:type="array">
               <item name="config" xsi:type="array">
                   <item name="indexField" xsi:type="string">entity_id</item>
                   <item name="sorting" xsi:type="string">desc</item>
                   <item name="sortOrder" xsi:type="number">0</item>
               </item>
           </argument>
       </selectionsColumn>
       <column name="task_id">
           <argument name="data" xsi:type="array">
               <item name="config" xsi:type="array">
                   <item name="filter" xsi:type="string">integer</item>
                   <item name="label" xsi:type="string" translate="true">Task Id</item>
               </item>
           </argument>
       </column>
       <column name="item_cost" >
           <argument name="data" xsi:type="array">
               <item name="config" xsi:type="array">
                   <item name="filter" xsi:type="string">text</item>
                   <item name="label" xsi:type="string" translate="true">Item Cost</item>
               </item>
           </argument>
       </column>
       <column name="item_currency" >
           <argument name="data" xsi:type="array">
               <item name="config" xsi:type="array">
                   <item name="filter" xsi:type="string">text</item>
                   <item name="label" xsi:type="string" translate="true">Item Currency</item>
               </item>
           </argument>
       </column>
       
       <column name="item_reward" >
           <argument name="data" xsi:type="array">
               <item name="config" xsi:type="array">
                   <item name="filter" xsi:type="string">text</item>
                   <item name="label" xsi:type="string" translate="true">Item Reward</item>
               </item>
           </argument>
       </column>

       <column name="is_active" >
           <argument name="data" xsi:type="array">
               <item name="options" xsi:type="object">Coditron\Lixtask\Model\Status</item>
               <item name="config" xsi:type="array">
                   <item name="filter" xsi:type="string">select</item>
                   <item name="component" xsi:type="string">Magento_Ui/js/grid/columns/select</item>
                   <item name="dataType" xsi:type="string">select</item>
                   <item name="label" xsi:type="string" translate="true">Status</item>
               </item>
           </argument>
       </column>
      
       <!-- Add Action with each row of grid and for this we will create a class Action -->
       <actionsColumn name="actions" class="Coditron\Lixtask\Ui\Component\Listing\Column\Action">
           <argument name="data" xsi:type="array">
               <item name="config" xsi:type="array">
                   <item name="resizeEnabled" xsi:type="boolean">false</item>
                   <item name="resizeDefaultWidth" xsi:type="string">107</item>
                   <item name="indexField" xsi:type="string">id</item>
               </item>
           </argument>
       </actionsColumn>
    </columns>
</listing>
