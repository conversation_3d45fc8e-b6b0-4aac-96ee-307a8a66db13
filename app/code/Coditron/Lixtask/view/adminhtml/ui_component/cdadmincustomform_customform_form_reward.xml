<?xml version="1.0" ?>
<form xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
	<argument name="data" xsi:type="array">
		<item name="js_config" xsi:type="array">
			<item name="provider" xsi:type="string">cdadmincustomform_customform_form.customform_form_data_source</item>
		</item>
		<item name="label" xsi:type="string" translate="true">General Information</item>
		<item name="template" xsi:type="string">templates/form/collapsible</item>
	</argument>
	<settings>
		<buttons>
			<button name="back" class="Coditron\Lixtask\Block\Adminhtml\Customform\Edit\BackButton"/>
			<button name="delete" class="Coditron\Lixtask\Block\Adminhtml\Customform\Edit\DeleteButton"/>
			<button name="reset" class="Coditron\Lixtask\Block\Adminhtml\Customform\Edit\ResetButton"/>
			<button name="save" class="Coditron\Lixtask\Block\Adminhtml\Customform\Edit\SaveButton"/>
			<button name="save_and_continue" class="Coditron\Lixtask\Block\Adminhtml\Customform\Edit\SaveAndContinueButton"/>
		</buttons>
		<namespace>cdadmincustomform_customform_form</namespace>
		<dataScope>data</dataScope>
		<deps>
			<dep>cdadmincustomform_customform_form.customform_form_data_source</dep>
		</deps>
	</settings>
	<dataSource name="customform_form_data_source">
		<argument name="data" xsi:type="array">
			<item name="js_config" xsi:type="array">
				<item name="component" xsi:type="string">Magento_Ui/js/form/provider</item>
			</item>
		</argument>
		<settings>
			<submitUrl path="*/*/save"/>
		</settings>
		<dataProvider name="customform_form_data_source" class="Coditron\Lixtask\Model\Customform\DataProvider">
			<settings>
				<requestFieldName>id</requestFieldName>
				<primaryFieldName>id</primaryFieldName>
			</settings>
		</dataProvider>
	</dataSource>
	<fieldset name="general">
		<settings>
			<label>General</label>
		</settings>
		<field name="task_id" sortOrder="10">
		    <argument name="data" xsi:type="array">
		        <item name="config" xsi:type="array">
		            <item name="dataType" xsi:type="string">text</item>
		            <item name="formElement" xsi:type="string">input</item>
		            <item name="source" xsi:type="string">Customform</item>
		            <item name="label" xsi:type="string" translate="true">Task ID</item>
		            <item name="dataScope" xsi:type="string">task_id</item>
		        	<item name="tooltip" xsi:type="array">
		                <item name="description" xsi:type="string" translate="true">ID of task to create reward for.</item>
		        	</item>
				</item>
		    </argument>
		    <settings>
		        <dataType>integer</dataType>
		        <label translate="true">Task ID</label>
		        <dataScope>task_id</dataScope>
		        <validation>
					<rule name="required-entry" xsi:type="boolean">true</rule>
				</validation>
		    </settings>
		</field>
		<field name="project_id" formElement="input" sortOrder="20">
			<argument name="data" xsi:type="array">
				<item name="config" xsi:type="array">
					<item name="source" xsi:type="string">Customform</item>
					<item name="tooltip" xsi:type="array">
		            <item name="description" xsi:type="string" translate="true">Provide your Campaign/Project ID you created above.</item>
		        	</item>
		        	 <item name="dataScope" xsi:type="string">project_id</item>
                    <item name="formElement" xsi:type="string">input</item>
				</item>
			</argument>
			<settings>
				<dataType>text</dataType>
				<label translate="true">Project Id </label>
				<dataScope>project_id</dataScope>
				<validation>
					<rule name="required-entry" xsi:type="boolean">true</rule>
				</validation>
			</settings>
		</field>

		<field name="item_cost" formElement="input" sortOrder="20">
			<argument name="data" xsi:type="array">
				<item name="config" xsi:type="array">
					<item name="source" xsi:type="string">Customform</item>
					<item name="tooltip" xsi:type="array">
		            <item name="description" xsi:type="string" translate="true">When a customer spends up to this amount, they will be rewarded with the percentage or ratio value in the item reward field?</item>
		        	</item>
                    <item name="formElement" xsi:type="string">input</item>
				</item>
			</argument>
			<settings>
				<dataType>text</dataType>
				<label translate="true">Item Cost</label>
				<dataScope>item_cost</dataScope>
				<validation>
					<rule name="required-entry" xsi:type="boolean">true</rule>
				</validation>
			</settings>
		</field>
		<field name="item_currency " formElement="input" sortOrder="30">
			<argument name="data" xsi:type="array">
				<item name="config" xsi:type="array">
					<item name="source" xsi:type="string">Customform</item>
					<item name="tooltip" xsi:type="array">
		            <item name="description" xsi:type="string" translate="true">Enter the local currency customer will spend to earn reward. currency code i.e USD, GBP, EUR</item>
		        	</item>
                    <item name="formElement" xsi:type="string">input</item>
				</item>
			</argument>
			<settings>
				<dataType>text</dataType>
				<label translate="true">Item Currency </label>
				<dataScope>item_currency </dataScope>
				<validation>
					<rule name="required-entry" xsi:type="boolean">true</rule>
				</validation>
			</settings>
		</field>
		<field name="item_reward  " formElement="input" sortOrder="40">
			<argument name="data" xsi:type="array">
				<item name="config" xsi:type="array">
					<item name="source" xsi:type="string">Customform</item>
					<item name="tooltip" xsi:type="array">
		            <item name="description" xsi:type="string" translate="true">How much LUSD should the member be rewarded with? percentage or ratio value</item>
		        	</item>
                    <item name="formElement" xsi:type="string">input</item>
				</item>
			</argument>
			<settings>
				<dataType>text</dataType>
				<label translate="true">Item Reward </label>
				<dataScope>item_reward</dataScope>
				<validation>
					<rule name="required-entry" xsi:type="boolean">true</rule>
				</validation>
			</settings>
		</field>
		
	</fieldset>
</form>
