<?xml version="1.0" encoding="UTF-8"?>
<!--
/**
 * Grid record list UI Component
 * @category  Webkul
 * @package   Webkul_Grid
 * <AUTHOR>
 * @copyright Copyright (c) 2010-2017 Webkul Software Private Limited (https://webkul.com)
 * @license   https://store.webkul.com/license.html
 */
-->
<listing xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:Ui/etc/ui_configuration.xsd">
    <argument name="data" xsi:type="array">
        <item name="js_config" xsi:type="array">
            <item name="provider" xsi:type="string">cdadmincustomform_scrachcard_list.cdadmincustomform_scrachcard_list_data_source</item>
            <item name="deps" xsi:type="string">cdadmincustomform_scrachcard_list.cdadmincustomform_scrachcard_list_data_source</item>
        </item>
        <item name="spinner" xsi:type="string">scrachcard_records_columns</item>
        <item name="buttons" xsi:type="array">
            <item name="scratchcard" xsi:type="array">
                <item name="name" xsi:type="string">scratchcard</item>
                <item name="label" xsi:type="string" translate="true">Add Scratch Card</item>
                <item name="class" xsi:type="string">primary</item>
                <item name="url" xsi:type="string">*/*/create</item>
            </item>
            <item name="scratchcardsync" xsi:type="array">
                <item name="name" xsi:type="string">scratchcardsync</item>
                <item name="label" xsi:type="string" translate="true">Sync Task</item>
                <item name="class" xsi:type="string">primary</item>
                <item name="url" xsi:type="string">cdadmincustomform/scrachcard/synctask</item>
            </item>
        </item>
    </argument>
 <dataSource name="cdadmincustomform_scrachcard_list_data_source">
        <argument name="dataProvider" xsi:type="configurableObject">
            <argument name="class" xsi:type="string">Magento\Framework\View\Element\UiComponent\DataProvider\DataProvider</argument>
            <argument name="name" xsi:type="string">cdadmincustomform_scrachcard_list_data_source</argument>
            <argument name="primaryFieldName" xsi:type="string">id</argument>
            <argument name="requestFieldName" xsi:type="string">id</argument>
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="update_url" xsi:type="url" path="mui/index/render"/>
                    <item name="storageConfig" xsi:type="array">
                        <item name="indexField" xsi:type="string">id</item>
                    </item>
                </item>
            </argument>
        </argument>
        <argument name="data" xsi:type="array">
            <item name="js_config" xsi:type="array">
                <item name="component" xsi:type="string">Magento_Ui/js/grid/provider</item>
            </item>
        </argument>
    </dataSource>
    <container name="listing_top">
        <argument name="data" xsi:type="array">
            <item name="config" xsi:type="array">
                <item name="template" xsi:type="string">ui/grid/toolbar</item>
                <item name="stickyTmpl" xsi:type="string">ui/grid/sticky/toolbar</item>
            </item>
        </argument>
        <bookmark name="bookmarks"/>
        <columnsControls name="columns_controls"/>
        <filters name="listing_filters">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="columnsProvider" xsi:type="string">cdadmincustomform_scrachcard_list.cdadmincustomform_scrachcard_list.scrachcard_records_columns</item>
                    <item name="storageConfig" xsi:type="array">
                        <item name="provider" xsi:type="string">cdadmincustomform_scrachcard_list.cdadmincustomform_scrachcard_list.listing_top.bookmarks</item>
                        <item name="namespace" xsi:type="string">current.filters</item>
                    </item>
                    <item name="templates" xsi:type="array">
                        <item name="filters" xsi:type="array">
                            <item name="select" xsi:type="array">
                                <item name="component" xsi:type="string">Magento_Ui/js/form/element/ui-select</item>
                                <item name="template" xsi:type="string">ui/grid/filters/elements/ui-select</item>
                            </item>
                        </item>
                    </item>
                    <item name="childDefaults" xsi:type="array">
                        <item name="provider" xsi:type="string">cdadmincustomform_scrachcard_list.cdadmincustomform_scrachcard_list.listing_top.listing_filters</item>
                        <item name="imports" xsi:type="array">
                            <item name="visible" xsi:type="string">cdadmincustomform_scrachcard_list.cdadmincustomform_scrachcard_list.scrachcard_records_columns.${ $.index }:visible</item>
                        </item>
                    </item>
                </item>
                <item name="observers" xsi:type="array">
                    <item name="column" xsi:type="string">column</item>
                </item>
            </argument>
        </filters>
        <massaction name="listing_massaction">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="selectProvider" xsi:type="string">cdadmincustomform_scrachcard_list.cdadmincustomform_scrachcard_list.scrachcard_records_columns.ids</item>
                    <item name="component" xsi:type="string">Magento_Ui/js/grid/tree-massactions</item>
                    <item name="indexField" xsi:type="string">id</item>
                </item>
            </argument>
            <!-- Mass actions which you want to add in your grid-->
            <action name="delete">
                <argument name="data" xsi:type="array">
                    <item name="config" xsi:type="array">
                        <item name="type" xsi:type="string">delete</item>
                        <item name="label" xsi:type="string" translate="true">Delete</item>
                       <item name="url" xsi:type="url" path="cdadmincustomform/scrachcard/massdelete"/>
                        <item name="confirm" xsi:type="array">
                            <item name="title" xsi:type="string" translate="true">Delete</item>
                            <item name="message" xsi:type="string" translate="true">Do you want to delete selected row record?</item>
                        </item>
                    </item>
                </argument>
            </action>
            <action name="enable">
                <argument name="data" xsi:type="array">
                    <item name="config" xsi:type="array">
                        <item name="type" xsi:type="string">enable</item>
                        <item name="label" xsi:type="string" translate="true">Enable</item>
                       <item name="url" xsi:type="url" path="cdadmincustomform/scrachcard/massenable"/>
                        <item name="confirm" xsi:type="array">
                            <item name="title" xsi:type="string" translate="true">Enable</item>
                            <item name="message" xsi:type="string" translate="true">Do you want to enable selected row record?</item>
                        </item>
                    </item>
                </argument>
            </action>

             <action name="disable">
                <argument name="data" xsi:type="array">
                    <item name="config" xsi:type="array">
                        <item name="type" xsi:type="string">disable</item>
                        <item name="label" xsi:type="string" translate="true">Disable</item>
                       <item name="url" xsi:type="url" path="cdadmincustomform/scrachcard/massdisable"/>
                        <item name="confirm" xsi:type="array">
                            <item name="title" xsi:type="string" translate="true">Disable</item>
                            <item name="message" xsi:type="string" translate="true">Do you want to disable selected row record?</item>
                        </item>
                    </item>
                </argument>
            </action>


        </massaction>
        <paging name="listing_paging">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="storageConfig" xsi:type="array">
                        <item name="provider" xsi:type="string">cdadmincustomform_scrachcard_list.cdadmincustomform_scrachcard_list.listing_top.bookmarks</item>
                        <item name="namespace" xsi:type="string">current.paging</item>
                    </item>
                    <item name="selectProvider" xsi:type="string">cdadmincustomform_scrachcard_list.cdadmincustomform_scrachcard_list.scrachcard_records_columns.ids</item>
                </item>
            </argument>
        </paging>
    </container>
    <columns name="scrachcard_records_columns">
        <selectionsColumn name="ids">
           <argument name="data" xsi:type="array">
               <item name="config" xsi:type="array">
                   <item name="indexField" xsi:type="string">id</item>
                   <item name="sorting" xsi:type="string">desc</item>
                   <item name="sortOrder" xsi:type="number">0</item>
               </item>
           </argument>
       </selectionsColumn>

       <column name="task_id" >
           <argument name="data" xsi:type="array">
               <item name="config" xsi:type="array">
                   <item name="filter" xsi:type="string">text</item>
                   <item name="label" xsi:type="string" translate="true">Task Id</item>
               </item>
           </argument>
       </column>
      
       <column name="project_id" >
           <argument name="data" xsi:type="array">
               <item name="config" xsi:type="array">
                   <item name="filter" xsi:type="string">text</item>
                   <item name="label" xsi:type="string" translate="true">Project Id</item>
               </item>
           </argument>
       </column>

       <column name="title" >
           <argument name="data" xsi:type="array">
               <item name="config" xsi:type="array">
                   <item name="filter" xsi:type="string">text</item>
                   <item name="label" xsi:type="string" translate="true">Title</item>
               </item>
           </argument>
       </column>

    <!--   <column name="custom_currency_organisation_id" >
           <argument name="data" xsi:type="array">
               <item name="config" xsi:type="array">
                   <item name="filter" xsi:type="string">text</item>
                   <item name="label" xsi:type="string" translate="true">Custom Currency Organisation Id</item>
               </item>
           </argument>
       </column> -->

        <column name="custom_currency_organisation_id" >
           <argument name="data" xsi:type="array">
               <item name="options" xsi:type="object">Coditron\Lixtask\Model\Orgid</item>
               <item name="config" xsi:type="array">
                   <item name="filter" xsi:type="string">select</item>
                   <item name="component" xsi:type="string">Magento_Ui/js/grid/columns/select</item>
                   <item name="dataType" xsi:type="string">select</item>
                   <item name="label" xsi:type="string" translate="true">Custom Currency Organisation Id</item>
               </item>
           </argument>
       </column>

       <column name="total_coins_available" >
           <argument name="data" xsi:type="array">
               <item name="config" xsi:type="array">
                   <item name="filter" xsi:type="string">text</item>
                   <item name="label" xsi:type="string" translate="true">Total Coins Available</item>
               </item>
           </argument>
       </column>

       <column name="reward_amounts" >
           <argument name="data" xsi:type="array">
               <item name="config" xsi:type="array">
                   <item name="filter" xsi:type="string">text</item>
                   <item name="label" xsi:type="string" translate="true">Reward Amounts</item>
               </item>
           </argument>
       </column>

       <column name="approval_type" >
           <argument name="data" xsi:type="array">
               <item name="config" xsi:type="array">
                   <item name="filter" xsi:type="string">text</item>
                   <item name="label" xsi:type="string" translate="true">Approval Type</item>
               </item>
           </argument>
       </column>

       <column name="reward_expires_in" >
           <argument name="data" xsi:type="array">
               <item name="config" xsi:type="array">
                   <item name="filter" xsi:type="string">text</item>
                   <item name="label" xsi:type="string" translate="true">Reward Expires In</item>
               </item>
           </argument>
       </column>

       <column name="has_location_trigger" class="Coditron\Lixtask\Ui\Component\Listing\Column\HasLocationTrigger">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="filter" xsi:type="string">text</item>
                    <item name="label" xsi:type="string" translate="true">Has Location Trigger</item>
                </item>
            </argument>
        </column>

        <column name="location_id" >
           <argument name="data" xsi:type="array">
               <item name="config" xsi:type="array">
                   <item name="filter" xsi:type="string">text</item>
                   <item name="label" xsi:type="string" translate="true">Location Id</item>
               </item>
           </argument>
       </column>

        <column name="schedule_local_time" >
           <argument name="data" xsi:type="array">
               <item name="config" xsi:type="array">
                   <item name="filter" xsi:type="string">text</item>
                   <item name="label" xsi:type="string" translate="true">Schedule Local Time</item>
               </item>
           </argument>
       </column>

        <column name="time_zone" >
           <argument name="data" xsi:type="array">
               <item name="config" xsi:type="array">
                   <item name="filter" xsi:type="string">text</item>
                   <item name="label" xsi:type="string" translate="true">Time Zone</item>
               </item>
           </argument>
       </column>
       
       <column name="status" >
           <argument name="data" xsi:type="array">
               <item name="options" xsi:type="object">Coditron\Lixtask\Model\Status</item>
               <item name="config" xsi:type="array">
                   <item name="filter" xsi:type="string">select</item>
                   <item name="component" xsi:type="string">Magento_Ui/js/grid/columns/select</item>
                   <item name="dataType" xsi:type="string">select</item>
                   <item name="label" xsi:type="string" translate="true">Status</item>
               </item>
           </argument>
       </column>

       <!-- Add Action with each row of grid and for this we will create a class Action -->
     <actionsColumn name="actions" class="Coditron\Lixtask\Ui\Component\Listing\Column\Scrachcardaction">
           <argument name="data" xsi:type="array">
               <item name="config" xsi:type="array">
                   <item name="resizeEnabled" xsi:type="boolean">false</item>
                   <item name="resizeDefaultWidth" xsi:type="string">107</item>
                   <item name="indexField" xsi:type="string">id</item>
               </item>
           </argument>
       </actionsColumn>
    </columns>
</listing>
