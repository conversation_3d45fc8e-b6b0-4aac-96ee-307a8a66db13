<?xml version="1.0" ?>
<form xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
	<argument name="data" xsi:type="array">
		<item name="js_config" xsi:type="array">
			<item name="provider" xsi:type="string">cdadmincustomform_customform_form.customform_form_data_source</item>
		</item>
		<item name="label" xsi:type="string" translate="true">General Information</item>
		<item name="template" xsi:type="string">templates/form/collapsible</item>
	</argument>
	<settings>
		<buttons>
			<button name="back" class="Coditron\Lixtask\Block\Adminhtml\Customform\Edit\BackButton"/>
			<button name="delete" class="Coditron\Lixtask\Block\Adminhtml\Customform\Edit\DeleteButton"/>
			<button name="reset" class="Coditron\Lixtask\Block\Adminhtml\Customform\Edit\ResetButton"/>
			<button name="save" class="Coditron\Lixtask\Block\Adminhtml\Customform\Edit\SaveButton"/>
			<button name="save_and_continue" class="Coditron\Lixtask\Block\Adminhtml\Customform\Edit\SaveAndContinueButton"/>
		</buttons>
		<namespace>cdadmincustomform_customform_form</namespace>
		<dataScope>data</dataScope>
		<deps>
			<dep>cdadmincustomform_customform_form.customform_form_data_source</dep>
		</deps>
	</settings>
	<dataSource name="customform_form_data_source">
		<argument name="data" xsi:type="array">
			<item name="js_config" xsi:type="array">
				<item name="component" xsi:type="string">Magento_Ui/js/form/provider</item>
			</item>
		</argument>
		<settings>
			<submitUrl path="*/*/save"/>
		</settings>
		<dataProvider name="customform_form_data_source" class="Coditron\Lixtask\Model\Customform\DataProvider">
			<settings>
				<requestFieldName>id</requestFieldName>
				<primaryFieldName>id</primaryFieldName>
			</settings>
		</dataProvider>
	</dataSource>
	<fieldset name="general">
		<settings>
			<label>General</label>
		</settings>
		<field name="task_id" sortOrder="999" formElement="hidden">
		    <argument name="data" xsi:type="array">
		        <item name="config" xsi:type="array">
		            <item name="dataType" xsi:type="string">text</item>
		            <item name="formElement" xsi:type="string">input</item>
		            <item name="source" xsi:type="string">Customform</item>
		            <item name="label" xsi:type="string" translate="true">Task ID</item>
		            <item name="dataScope" xsi:type="string">task_id</item>
		        </item>
		    </argument>
		    <settings>
		        <dataType>text</dataType>
		        <label translate="true">Task ID</label>
		        <dataScope>task_id</dataScope>
		        <visible>false</visible>
		    </settings>
		</field>
		<field name="project_id" formElement="input" sortOrder="20">
			<argument name="data" xsi:type="array">
				<item name="config" xsi:type="array">
					<item name="source" xsi:type="string">Customform</item>
					<item name="tooltip" xsi:type="array">
		            <item name="description" xsi:type="string" translate="true">Provide your Campaign/Project ID you created above.</item>
		        	</item>
		        	 <item name="dataScope" xsi:type="string">project_id</item>
                    <item name="formElement" xsi:type="string">input</item>
				</item>
			</argument>
			<settings>
				<dataType>text</dataType>
				<label translate="true">Project Id </label>
				<dataScope>project_id</dataScope>
				<validation>
					<rule name="required-entry" xsi:type="boolean">true</rule>
				</validation>
			</settings>
		</field>

		<field name="custom_currency_organisation_id" formElement="select" sortOrder="30">
		    <argument name="data" xsi:type="array">
		        <item name="options" xsi:type="array">
		            <item name="variable" xsi:type="array">
		                <item name="label" xsi:type="string" translate="true">LIXCA</item>
		                <item name="value" xsi:type="string">2733</item>
		            </item>
		            <item name="percentage" xsi:type="array">
		                <item name="label" xsi:type="string" translate="true">LIXX</item>
		                <item name="value" xsi:type="string">1724</item>
		            </item>
		        </item>
		        <item name="config" xsi:type="array">
		            <item name="dataType" xsi:type="string">text</item>
		            <item name="label" xsi:type="string" translate="true">Custom Currency Organisation Id</item>
		            <item name="formElement" xsi:type="string">select</item>
		            <item name="source" xsi:type="string">Customform</item>
					<item name="tooltip" xsi:type="array">
		                <item name="description" xsi:type="string" translate="true">Provide your Organisation wallet ID i.e You can get wallet ID from the organisation show endpoint above.</item>
		        	</item>
		        </item>
		    </argument>
		    <settings>
		        <dataType>text</dataType>
		        <label translate="true">Custom Currency Organisation Id</label>
		        <dataScope>custom_currency_organisation_id</dataScope>
		        <validation>
		            <rule name="required-entry" xsi:type="boolean">true</rule>
		        </validation>
		    </settings>
		</field>
		<field name="task_title" formElement="textarea" sortOrder="40">
			<argument name="data" xsi:type="array">
				<item name="config" xsi:type="array">
					<item name="source" xsi:type="string">Customform</item>
					<item name="tooltip" xsi:type="array">
		                <item name="description" xsi:type="string" translate="true">Give your task a title.</item>
		        	</item>
				</item>
			</argument>
			<settings>
				<dataType>text</dataType>
				<label translate="true">Task Title</label>
				<dataScope>task_title</dataScope>
				<validation>
					<rule name="required-entry" xsi:type="boolean">true</rule>
				</validation>
			</settings>
		</field>
		<field name="task_title_show" formElement="textarea" sortOrder="40">
			<argument name="data" xsi:type="array">
				<item name="config" xsi:type="array">
					<item name="source" xsi:type="string">Customform</item>
					<item name="tooltip" xsi:type="array">
		                <item name="description" xsi:type="string" translate="true">Give your task a title to show.</item>
		        	</item>
				</item>
			</argument>
			<settings>
				<dataType>text</dataType>
				<label translate="true">Frontend Task Title</label>
				<dataScope>task_title_show</dataScope>
				<validation>

				</validation>
			</settings>
		</field>
		<field name="task_url" formElement="textarea" sortOrder="50">
			<argument name="data" xsi:type="array">
				<item name="config" xsi:type="array">
					<item name="source" xsi:type="string">Customform</item>
					<item name="tooltip" xsi:type="array">
		                <item name="description" xsi:type="string" translate="true">Give your task url.</item>
		        	</item>
				</item>
			</argument>
			<settings>
				<dataType>text</dataType>
				<label translate="true">Task Url</label>
				<dataScope>task_url</dataScope>
				<validation>

				</validation>
			</settings>
		</field>
		<field name="task_button_label" formElement="textarea" sortOrder="50">
			<argument name="data" xsi:type="array">
				<item name="config" xsi:type="array">
					<item name="source" xsi:type="string">Customform</item>
					<item name="tooltip" xsi:type="array">
		                <item name="description" xsi:type="string" translate="true">Add label for task</item>
		        	</item>
				</item>
			</argument>
			<settings>
				<dataType>text</dataType>
				<label translate="true">Task Label</label>
				<dataScope>task_button_label</dataScope>
				<validation>

				</validation>
			</settings>
		</field>

		<field name="reward_type" formElement="select" sortOrder="50">
		    <argument name="data" xsi:type="array">
		        <item name="options" xsi:type="array">
					<item name="fixed" xsi:type="array">
		                <item name="label" xsi:type="string" translate="true">Fixed</item>
		                <item name="value" xsi:type="string">fixed</item>
		            </item>
		            <item name="variable" xsi:type="array">
		                <item name="label" xsi:type="string" translate="true">Variable</item>
		                <item name="value" xsi:type="string">variable</item>
		            </item>
		            <item name="percentage" xsi:type="array">
		                <item name="label" xsi:type="string" translate="true">Percentage</item>
		                <item name="value" xsi:type="string">percentage</item>
		            </item>
		            <item name="ratio" xsi:type="array">
		                <item name="label" xsi:type="string" translate="true">Ratio</item>
		                <item name="value" xsi:type="string">ratio</item>
		            </item>
		        </item>
		        <item name="config" xsi:type="array">
		            <item name="dataType" xsi:type="string">text</item>
		            <item name="label" xsi:type="string" translate="true">Reward Type</item>
		            <item name="formElement" xsi:type="string">select</item>
		            <item name="source" xsi:type="string">Customform</item>
					<item name="tooltip" xsi:type="array">
						<item name="description" xsi:type="string" translate="true">Set reward type, when activity is submitted Fixed: This means that when your customer submits an activity, we'll pay the customer the exact anounts you entered in the reward-per-action field below, Your API should just send us the customer email and the task ID they completed.
						* Variable: Here you have to send us the customer email, task ID and the amounts, otherwise we'll pay the customer whatever you entered in the reward-per-action field below. (API usage only)
						* Percentage: If you choose this option, in the next page you will be ask to create different reward rates, depending on the amount each customer spend. Eg. users who spent upto 1,000 USD in your shop will be rewarded with 10 points while users who spent 2,000 USD will be rewarded with 20 points. (API usage only)
						* Ratio: Ratio reward option, allows you to create reward ratio configuration, depending on the spending amount of each customers. Eg. For every customer who spent minimum of 1USD in your shop will be rewarded with 1 points. (API usage only)
						</item>
		        	</item>
					<item name="default" xsi:type="string">variable</item>
		        </item>

		    </argument>
		    <settings>
		        <dataType>text</dataType>
		        <label translate="true">Reward Type</label>
		        <dataScope>reward_type</dataScope>
		        <validation>
		            <rule name="required-entry" xsi:type="boolean">true</rule>
		        </validation>
		    </settings>
		</field>
		<field name="coins_per_action" formElement="input" sortOrder="60">
			<argument name="data" xsi:type="array">
				<item name="config" xsi:type="array">
					<item name="source" xsi:type="string">Customform</item>
					<item name="tooltip" xsi:type="array">
		                <item name="description" xsi:type="string" translate="true">What is the minimum points each user will be rewarded for completing this task.?</item>
		        	</item>
				</item>
			</argument>
			<settings>
				<dataType>integer</dataType>
				<label translate="true">Coins Per Action</label>
				<dataScope>coins_per_action</dataScope>
				<validation>
					<rule name="required-entry" xsi:type="boolean">true</rule>
				</validation>
			</settings>
		</field>
        <field name="xp_points" formElement="input" sortOrder="65">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="source" xsi:type="string">Customform</item>
                    <item name="tooltip" xsi:type="array">
                        <item name="description" xsi:type="string" translate="true">XP Points that the user receives after completing the task</item>
                    </item>
                </item>
            </argument>
            <settings>
                <dataType>integer</dataType>
                <label translate="true">XP Points Per Action</label>
                <dataScope>xp_points</dataScope>
                <validation>
                    <rule name="required-entry" xsi:type="boolean">true</rule>
                </validation>
            </settings>
        </field>
		<field name="total_coins_available" formElement="input" sortOrder="70">
			<argument name="data" xsi:type="array">
				<item name="config" xsi:type="array">
					<item name="source" xsi:type="string">Customform</item>
					<item name="tooltip" xsi:type="array">
		                <item name="description" xsi:type="string" translate="true">Total points allocated for this task. i.e 5000</item>
		        	</item>
				</item>
			</argument>
			<settings>
				<dataType>integer</dataType>
				<label translate="true">Total Coins Available</label>
				<dataScope>total_coins_available</dataScope>
			</settings>
		</field>
		<field name="total_task_submission" formElement="input" sortOrder="80">
			<argument name="data" xsi:type="array">
				<item name="config" xsi:type="array">
					<item name="source" xsi:type="string">Customform</item>
					<item name="tooltip" xsi:type="array">
		            <item name="description" xsi:type="string" translate="true">Total number of users that can participate in the submission of this task. i.e 50000</item>
		        	</item>
				</item>
			</argument>
			<settings>
				<dataType>integer</dataType>
				<label translate="true">Total Task Submission</label>
				<dataScope>total_task_submission</dataScope>
			</settings>
		</field>

		<field name="parent_id" formElement="input" sortOrder="90">
			<argument name="data" xsi:type="array">
				<item name="config" xsi:type="array">
					<item name="source" xsi:type="string">Customform</item>
					<item name="tooltip" xsi:type="array">
		                <item name="description" xsi:type="string" translate="true">Is there another task that has to be completed before this one? That task must have been created under the same project with this task.</item>
		        	</item>
				</item>
			</argument>
			<settings>
				<dataType>text</dataType>
				<label translate="true">Parent Id</label>
				<dataScope>parent_id</dataScope>
			</settings>
		</field>
		<field name="description" formElement="textarea" sortOrder="100">
			<argument name="data" xsi:type="array">
				<item name="config" xsi:type="array">
					<item name="source" xsi:type="string">Customform</item>
					<item name="tooltip" xsi:type="array">
		                <item name="description" xsi:type="string" translate="true">Provide details instructions to the users who wish to complete this task.</item>
		        	</item>
				</item>
			</argument>
			<settings>
				<dataType>text</dataType>
				<label translate="true">Description</label>
				<dataScope>description</dataScope>
			</settings>
		</field>
		<field name="status" formElement="select" sortOrder="15">
		    <argument name="data" xsi:type="array">
		        <item name="options" xsi:type="array">
		            <item name="active" xsi:type="array">
		                <item name="label" xsi:type="string" translate="true">Active</item>
		                <item name="value" xsi:type="string">active</item>
		            </item>
		            <item name="inactive" xsi:type="array">
		                <item name="label" xsi:type="string" translate="true">Inactive</item>
		                <item name="value" xsi:type="string">inactive</item>
		            </item>
		        </item>
		        <item name="config" xsi:type="array">
		            <item name="dataType" xsi:type="string">text</item>
		            <item name="label" xsi:type="string" translate="true">Status</item>
		            <item name="formElement" xsi:type="string">select</item>
		            <item name="source" xsi:type="string">Customform</item>
		            <!-- Add tooltip here under the config section -->
		           <item name="tooltip" xsi:type="array">
		                <item name="description" xsi:type="string" translate="true">Select task status (If set to 'inactive', people will no longer be able to submit activities to this tasks)</item>
		            </item>
		        </item>
		        <item name="default" xsi:type="string">active</item> <!-- Set default value here -->
		    </argument>
		    <settings>
		        <dataType>text</dataType>
		        <label translate="true">Task Status</label>
		        <dataScope>status</dataScope>
		        <validation>
		            <rule name="required-entry" xsi:type="boolean">true</rule>
		        </validation>
		    </settings>
		</field>
		<field name="approval_type" formElement="select" sortOrder="110">
		    <argument name="data" xsi:type="array">
		        <item name="options" xsi:type="array">
		            <item name="automatic" xsi:type="array">
		                <item name="label" xsi:type="string" translate="true">Automatic</item>
		                <item name="value" xsi:type="string">automatic</item>
		            </item>
		            <item name="manual" xsi:type="array">
		                <item name="label" xsi:type="string" translate="true">Manual</item>
		                <item name="value" xsi:type="string">manual</item>
		            </item>
		        </item>
		        <item name="config" xsi:type="array">
		            <item name="dataType" xsi:type="string">text</item>
		            <item name="label" xsi:type="string" translate="true">Approval Type</item>
		            <item name="formElement" xsi:type="string">select</item>
		            <item name="source" xsi:type="string">Customform</item>
					<item name="tooltip" xsi:type="array">
		                <item name="description" xsi:type="string" translate="true">Should users who submitted this task be automatically rewarded, or will you supervise submissions before manually rewarding users.? Approval Type . i.e (manual, automatic)</item>
		        	</item>
		        </item>
				<item name="default" xsi:type="string">automatic</item>
		    </argument>
		    <settings>
		        <dataType>text</dataType>
		        <label translate="true">Approval Type</label>
		        <dataScope>approval_type</dataScope>
		        <validation>
		            <rule name="required-entry" xsi:type="boolean">true</rule>
		        </validation>
		    </settings>
		</field>
		<field name="proof_type" formElement="select" sortOrder="120">
		    <argument name="data" xsi:type="array">
		        <item name="options" xsi:type="array">
		            <item name="image" xsi:type="array">
		                <item name="label" xsi:type="string" translate="true">Image</item>
		                <item name="value" xsi:type="string">image</item>
		            </item>
		            <item name="text" xsi:type="array">
		                <item name="label" xsi:type="string" translate="true">Text</item>
		                <item name="value" xsi:type="string">text</item>
		            </item>
		            <item name="both" xsi:type="array">
		                <item name="label" xsi:type="string" translate="true">Both</item>
		                <item name="value" xsi:type="string">both</item>
		            </item>
		            <item name="nothing" xsi:type="array">
		                <item name="label" xsi:type="string" translate="true">Nothing</item>
		                <item name="value" xsi:type="string">nothing</item>
		            </item>
		        </item>
		        <item name="config" xsi:type="array">
		            <item name="dataType" xsi:type="string">text</item>
		            <item name="label" xsi:type="string" translate="true">Proof Type</item>
		            <item name="formElement" xsi:type="string">select</item>
		            <item name="source" xsi:type="string">Customform</item>
					<item name="tooltip" xsi:type="array">
		                <item name="description" xsi:type="string" translate="true">Select task submission proof type required to validate, if this task was completed by the user. (image, text, both, nothing)</item>
		        	</item>
		        </item>
				<item name="default" xsi:type="string">nothing</item>
		    </argument>
		    <settings>
		        <dataType>text</dataType>
		        <label translate="true">Proof Type</label>
		        <dataScope>proof_type</dataScope>
		        <validation>
		            <rule name="required-entry" xsi:type="boolean">true</rule>
		        </validation>
		    </settings>
		</field>
		<field name="task_method" formElement="select" sortOrder="130">
		    <argument name="data" xsi:type="array">
		        <item name="options" xsi:type="array">
		            <item name="api" xsi:type="array">
		                <item name="label" xsi:type="string" translate="true">API</item>
		                <item name="value" xsi:type="string">api</item>
		            </item>
		            <item name="web" xsi:type="array">
		                <item name="label" xsi:type="string" translate="true">Web</item>
		                <item name="value" xsi:type="string">web</item>
		            </item>
		        </item>
		        <item name="config" xsi:type="array">
		            <item name="dataType" xsi:type="string">text</item>
		            <item name="label" xsi:type="string" translate="true">Task Method</item>
		            <item name="formElement" xsi:type="string">select</item>
		            <item name="source" xsi:type="string">Customform</item>
					<item name="tooltip" xsi:type="array">
		                <item name="description" xsi:type="string" translate="true">Task submission method channel . i.e (API, WEB)</item>
		        	</item>
		        </item>
				<item name="default" xsi:type="string">api</item>
		    </argument>
		    <settings>
		        <dataType>text</dataType>
		        <label translate="true">Task Method</label>
		        <dataScope>task_method</dataScope>
		        <validation>
		            <rule name="required-entry" xsi:type="boolean">true</rule>
		        </validation>
		    </settings>
		</field>
		<field name="reward_expires_in" formElement="input" sortOrder="140">
			<argument name="data" xsi:type="array">
				<item name="config" xsi:type="array">
					<item name="source" xsi:type="string">Customform</item>
					<item name="tooltip" xsi:type="array">
                        <item name="description" xsi:type="string" translate="true">Number of days it will take for the reward to expire after earning. Leave at 0 for none expiring reward.</item>
                    </item>
				</item>
			</argument>
			<settings>
				<dataType>integer</dataType>
				<label translate="true">Reward Expires In (nr)</label>
				<dataScope>reward_expires_in</dataScope>
			</settings>
		</field>
		<field name="is_staff_approval" formElement="select" sortOrder="150">
		    <argument name="data" xsi:type="array">
		        <item name="options" xsi:type="array">
		            <item name="option1" xsi:type="array">
		                <item name="label" xsi:type="string" translate="true">0</item>
		                <item name="value" xsi:type="string">0</item>
		            </item>
		            <item name="option2" xsi:type="array">
		                <item name="label" xsi:type="string" translate="true">1</item>
		                <item name="value" xsi:type="string">1</item>
		            </item>
		            <!-- Add more options as needed -->
		        </item>
		        <item name="config" xsi:type="array">
		            <item name="dataType" xsi:type="string">text</item>
		            <item name="label" xsi:type="string" translate="true">Is Staff Approval</item>
		            <item name="formElement" xsi:type="string">select</item>
		            <item name="source" xsi:type="string">Customform</item>
		            <!-- Add tooltip here under the config section -->
		            <item name="tooltip" xsi:type="array">
		                <item name="description" xsi:type="string" translate="true">Make your brand staff verify and validate customer reward for task submission.</item>
		            </item>
		        </item>
		    </argument>
		    <settings>
		        <dataType>integer</dataType>
		        <label translate="true">Is Staff Approval</label>
		        <dataScope>is_staff_approval</dataScope>
		    </settings>
		</field>
		<field name="shared_organisation_id" formElement="input" sortOrder="170">
			<argument name="data" xsi:type="array">
				<item name="config" xsi:type="array">
					<item name="source" xsi:type="string">Customform</item>
					<item name="tooltip" xsi:type="array">
		                <item name="description" xsi:type="string" translate="true">Shared task with organisation: (Separate each organisation ID with comma i.e 2, 5, 6) *Give other organisations access to this task.</item>
		        	</item>
				</item>
			</argument>
			<settings>
				<dataType>text</dataType>
				<label translate="true">Shared Organisation Id</label>
				<dataScope>shared_organisation_id</dataScope>
			</settings>
		</field>


		<field name="number_of_submissions_allowed" formElement="select" sortOrder="180">
		    <argument name="data" xsi:type="array">
		        <item name="options" xsi:type="array">
		            <item name="single" xsi:type="array">
		                <item name="label" xsi:type="string" translate="true">Single</item>
		                <item name="value" xsi:type="string">single</item>
		            </item>
		            <item name="multiple" xsi:type="array">
		                <item name="label" xsi:type="string" translate="true">Multiple</item>
		                <item name="value" xsi:type="string">multiple</item>
		            </item>
		        </item>
		        <item name="config" xsi:type="array">
		            <item name="dataType" xsi:type="string">text</item>
		            <item name="label" xsi:type="string" translate="true">Number Of Submissions Allowed</item>
		            <item name="formElement" xsi:type="string">select</item>
		            <item name="source" xsi:type="string">Customform</item>
		            <!-- Add tooltip here under the config section -->
		            <item name="tooltip" xsi:type="array">
		                <item name="description" xsi:type="string" translate="true">How often can the same user/customer submit an activity for this task?</item>
		            </item>
		        </item>
		        <item name="default" xsi:type="string">multiple</item> <!-- Set default value here -->
		    </argument>
		    <settings>
		        <dataType>text</dataType>
		        <label translate="true">Number Of Submissions Allowed</label>
		        <dataScope>number_of_submissions_allowed</dataScope>
		        <validation>
		            <rule name="required-entry" xsi:type="boolean">true</rule>
		        </validation>
		    </settings>
		</field>

		<field name="period_allowed_for_submissions" formElement="select" sortOrder="50">
		    <argument name="data" xsi:type="array">
		        <item name="options" xsi:type="array">
		            <item name="unlimited" xsi:type="array">
		                <item name="label" xsi:type="string" translate="true">Unlimited</item>
		                <item name="value" xsi:type="string">unlimited</item>
		            </item>
		            <item name="minute" xsi:type="array">
		                <item name="label" xsi:type="string" translate="true">Minute</item>
		                <item name="value" xsi:type="string">minute</item>
		            </item>
		            <item name="hour" xsi:type="array">
		                <item name="label" xsi:type="string" translate="true">Hour</item>
		                <item name="value" xsi:type="string">hour</item>
		            </item>
		            <item name="6_hours" xsi:type="array">
		                <item name="label" xsi:type="string" translate="true">6 Hours</item>
		                <item name="value" xsi:type="string">6hours</item>
		            </item>
		        </item>
		        <item name="config" xsi:type="array">
		            <item name="dataType" xsi:type="string">text</item>
		            <item name="label" xsi:type="string" translate="true">Period Allowed For Submissions</item>
		            <item name="formElement" xsi:type="string">select</item>
		            <item name="source" xsi:type="string">Customform</item>
					<item name="tooltip" xsi:type="array">
		                <item name="description" xsi:type="string" translate="true">Select when this task can be submitted by the same user/customer.</item>
		        	</item>
		        </item>
		        <item name="default" xsi:type="string">unlimited</item> <!-- Set default value here -->
		    </argument>
		    <settings>
		        <dataType>text</dataType>
		        <label translate="true">Period Allowed For Submissions</label>
		        <dataScope>period_allowed_for_submissions</dataScope>
		        <validation>
		            <rule name="required-entry" xsi:type="boolean">true</rule>
		        </validation>
		    </settings>
		</field>
		<field name="number_of_submissions_allowed_per_period" formElement="input" sortOrder="50">
			<argument name="data" xsi:type="array">
				<item name="config" xsi:type="array">
					<item name="source" xsi:type="string">Customform</item>
					<item name="tooltip" xsi:type="array">
		                <item name="description" xsi:type="string" translate="true">How many times can the same person submit an activity under this task per period?</item>
		        	</item>
				</item>
				<item name="default" xsi:type="string">1</item>
			</argument>
			<settings>
				<dataType>integer</dataType>
				<label translate="true">Number Of Submissions Allowed Per Period</label>
				<dataScope>number_of_submissions_allowed_per_period</dataScope>
			</settings>
		</field><field name="image">
			<argument name="data" xsi:type="array">
				<item name="config" xsi:type="array">
					<item name="dataType" xsi:type="string">string</item>
					<item name="source" xsi:type="string">image</item>
					<item name="label" xsi:type="string" translate="true">Image</item>
					<item name="visible" xsi:type="boolean">true</item>
					<item name="formElement" xsi:type="string">fileUploader</item>
					<item name="previewTmpl" xsi:type="string">Coditron_Lixtask/image-preview</item>
					<item name="elementTmpl" xsi:type="string">ui/form/element/uploader/uploader</item>
					<item name="required" xsi:type="boolean">false</item>
					<item name="uploaderConfig" xsi:type="array">
						<item name="url" xsi:type="url" path="cdadmincustomform/customform_fileUploader/save"/>
					</item>
					<item name="tooltip" xsi:type="array">
		                <item name="description" xsi:type="string" translate="true">Provide task display cover image (a cover photo for the task)</item>
		        	</item>
				</item>
			</argument>
		  </field>
	</fieldset>
</form>
