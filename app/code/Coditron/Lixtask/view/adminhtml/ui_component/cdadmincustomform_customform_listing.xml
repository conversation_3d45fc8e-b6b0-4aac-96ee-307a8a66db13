<?xml version="1.0" ?>
<listing xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
	<argument name="data" xsi:type="array">
		<item name="js_config" xsi:type="array">
			<item name="provider" xsi:type="string">cdadmincustomform_customform_listing.coditron_lixtask_listing_data_source</item>
		</item>
	</argument>
	<settings>
		<spinner>coditron_lixtask_columns</spinner>
		<deps>
			<dep>cdadmincustomform_customform_listing.coditron_lixtask_listing_data_source</dep>
		</deps>
		<buttons>
			<button name="add">
				<url path="*/*/new"/>
				<class>primary</class>
				<label translate="true">Add New Task</label>
			</button>
			<button name="synctask">
				<url path="cdadmincustomform/customform/synctask"/>
				<class>primary</class>
				<label translate="true">Sync Task</label>
			</button>
		</buttons>
	</settings>
	<dataSource name="coditron_lixtask_listing_data_source" component="Magento_Ui/js/grid/provider">
		<settings>
            <storageConfig>
                <param name="indexField" xsi:type="string">id</param>
            </storageConfig>
            <updateUrl path="mui/index/render"/>
		</settings>
		<aclResource>Coditron_Lixtask::Customform</aclResource>
		<dataProvider name="coditron_lixtask_listing_data_source" class="Magento\Framework\View\Element\UiComponent\DataProvider\DataProvider">
			<settings>
				<requestFieldName>id</requestFieldName>
				<primaryFieldName>id</primaryFieldName>
			</settings>
		</dataProvider>
	</dataSource>
	<listingToolbar name="listing_top">
		<settings>
			<sticky>true</sticky>
		</settings>
		<bookmark name="bookmarks"/>
		<columnsControls name="columns_controls"/>
		<massaction name="listing_massaction">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="selectProvider" xsi:type="string">cdadmincustomform_customform_listing.cdadmincustomform_customform_listing.coditron_lixtask_columns.ids</item>
                    <item name="indexField" xsi:type="string">id</item>
                </item>
            </argument>
            <action name="delete">
                <argument name="data" xsi:type="array">
                    <item name="config" xsi:type="array">
                        <item name="type" xsi:type="string">delete</item>
                        <item name="label" xsi:type="string" translate="true">Delete</item>
                        <item name="url" xsi:type="url" path="cdadmincustomform/Customform/massDelete"/>
                        <item name="confirm" xsi:type="array">
                            <item name="name" xsi:type="string" translate="true">Delete items</item>
                            <item name="message" xsi:type="string" translate="true">Are you sure you wan't to delete selected items?</item>
                        </item>
                    </item>
                </argument>
            </action>
            <action name="edit">
                <argument name="data" xsi:type="array">
                    <item name="config" xsi:type="array">
                        <item name="type" xsi:type="string">edit</item>
                        <item name="label" xsi:type="string" translate="true">Edit</item>
                        <item name="callback" xsi:type="array">
                            <item name="provider" xsi:type="string">cdadmincustomform_customform_listing.cdadmincustomform_customform_listing.coditron_lixtask_columns_editor</item>
                            <item name="target" xsi:type="string">editSelected</item>
                        </item>
                    </item>
                </argument>
            </action>
            <action name="disable">
                <settings>
                    <url path="cdadmincustomform/Customform/massDisable"/>
                    <type>disable</type>
                    <label translate="true">Disable</label>
                </settings>
            </action>
            <action name="enable">
                <settings>
                    <url path="cdadmincustomform/Customform/massEnable"/>
                    <type>enable</type>
                    <label translate="true">Enable</label>
                </settings>
            </action>
        </massaction>
		<filters name="listing_filters"/>
		<paging name="listing_paging"/>
		<filterSearch name="fulltext">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="provider" xsi:type="string">cdadmincustomform_customform_listing.coditron_lixtask_listing_data_source</item>
                    <item name="chipsProvider" xsi:type="string">cdadmincustomform_customform_listing.cdadmincustomform_customform_listing.listing_top.listing_filters_chips</item>
                    <item name="storageConfig" xsi:type="array">
                        <item name="provider" xsi:type="string">cdadmincustomform_customform_listing.cdadmincustomform_customform_listing.listing_top.bookmarks</item>
                        <item name="namespace" xsi:type="string">current.search</item>
                    </item>
                </item>
            </argument>
        </filterSearch>
	</listingToolbar>
	<columns name="coditron_lixtask_columns">
		<settings>
			<editorConfig>
				<param name="selectProvider" xsi:type="string">cdadmincustomform_customform_listing.cdadmincustomform_customform_listing.coditron_lixtask_columns.ids</param>
				<param name="enabled" xsi:type="boolean">true</param>
				<param name="indexField" xsi:type="string">id</param>
				<param name="clientConfig" xsi:type="array">
					<item name="saveUrl" xsi:type="url" path="cdadmincustomform/Customform/inlineEdit"/>
					<item name="validateBeforeSave" xsi:type="boolean">false</item>
				</param>
			</editorConfig>
			<childDefaults>
				<param name="fieldAction" xsi:type="array">
					<item name="provider" xsi:type="string">cdadmincustomform_customform_listing.cdadmincustomform_customform_listing.coditron_lixtask_columns_editor</item>
					<item name="target" xsi:type="string">startEdit</item>
					<item name="params" xsi:type="array">
						<item name="0" xsi:type="string">${ $.$data.rowIndex }</item>
						<item name="1" xsi:type="boolean">true</item>
					</item>
				</param>
			</childDefaults>
		</settings>
		<selectionsColumn name="ids">
			<settings>
				<indexField>id</indexField>
			</settings>
		</selectionsColumn>
		<column name="id">
			<settings>
				<filter>text</filter>
				<sorting>asc</sorting>
				<label translate="true">ID</label>
			</settings>
		</column>
		<column name="task_id">
			<settings>
				<filter>text</filter>
				<label translate="true">Task ID</label>
				<editor>
					<editorType>text</editorType>
					<validation>
						<!-- <rule name="required-entry" xsi:type="boolean">false</rule> -->
					</validation>
				</editor>
			</settings>
		</column>
		<column name="task_title">
			<settings>
				<filter>text</filter>
				<label translate="true">Task Title</label>
				<editor>
					<editorType>text</editorType>
					<validation>
						<rule name="required-entry" xsi:type="boolean">false</rule>
					</validation>
				</editor>
			</settings>
		</column>
		<column name="project_id">
			<settings>
				<filter>text</filter>
				<label translate="true">Project Id</label>
				<editor>
					<editorType>text</editorType>
					<validation>
						<rule name="required-entry" xsi:type="boolean">false</rule>
					</validation>
				</editor>
			</settings>
		</column>
		<column name="custom_currency_organisation_id">
			<settings>
				<filter>text</filter>
				<label translate="true">Custom Currency Organisation Id</label>
				<editor>
					<editorType>text</editorType>
					<validation>
						<rule name="required-entry" xsi:type="boolean">false</rule>
					</validation>
				</editor>
			</settings>
		</column>
		<column name="reward_type">
			<settings>
				<filter>text</filter>
				<label translate="true">Reward Type</label>
				<editor>
					<editorType>text</editorType>
					<validation>
						<rule name="required-entry" xsi:type="boolean">false</rule>
					</validation>
				</editor>
			</settings>
		</column>
		<column name="coins_per_action">
			<settings>
				<filter>text</filter>
				<label translate="true">Coins Per Action</label>
				<editor>
					<editorType>text</editorType>
					<validation>
						<rule name="required-entry" xsi:type="boolean">false</rule>
					</validation>
				</editor>
			</settings>
		</column>
		<column name="approval_type">
			<settings>
				<filter>text</filter>
				<label translate="true">Approval Type</label>
				<editor>
					<editorType>text</editorType>
					<validation>
						<rule name="required-entry" xsi:type="boolean">false</rule>
					</validation>
				</editor>
			</settings>
		</column>
		<column name="proof_type">
			<settings>
				<filter>text</filter>
				<label translate="true">Proof Type</label>
				<editor>
					<editorType>text</editorType>
					<validation>
						<rule name="required-entry" xsi:type="boolean">false</rule>
					</validation>
				</editor>
			</settings>
		</column>

		<column name="image" class="Coditron\Lixtask\Ui\Component\Listing\Column\Thumbnail">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="component" xsi:type="string">Magento_Ui/js/grid/columns/thumbnail</item>
                    <item name="sortable" xsi:type="boolean">false</item>
                    <item name="has_preview" xsi:type="string">1</item>
                    <item name="label" xsi:type="string" translate="true">Logo</item>
                </item>
            </argument>
        </column>
		<!-- <column name="status">
            <argument name="data" xsi:type="array">
                <item name="options" xsi:type="object">Coditron\Lixtask\Model\Source\Status</item>
                <item name="config" xsi:type="array">
                    <item name="editor" xsi:type="array">
                        <item name="editorType" xsi:type="string">select</item>
                        <item name="validation" xsi:type="array">
                            <item name="required-entry" xsi:type="boolean">true</item>
                        </item>
                    </item>
                    <item name="filter" xsi:type="string">select</item>
                    <item name="component" xsi:type="string">Magento_Ui/js/grid/columns/select</item>
                    <item name="label" xsi:type="string" translate="true">Status</item>
                    <item name="dataType" xsi:type="string">select</item>
                    <item name="sortOrder" xsi:type="number">30</item>
                    <item name="bodyTmpl" xsi:type="string">ui/grid/cells/html</item>
                </item>
            </argument>
        </column> -->
		<column name="created_at" class="Magento\Ui\Component\Listing\Columns\Date" component="Magento_Ui/js/grid/columns/date">
			<settings>
				<filter>dateRange</filter>
				<dataType>date</dataType>
				<label translate="true">created_at</label>
				<editor>
					<editorType>date</editorType>
					<validation>
						<rule name="required-entry" xsi:type="boolean">false</rule>
					</validation>
				</editor>
			</settings>
		</column>
		<!-- <column name="phone">
			<settings>
				<filter>text</filter>
				<label translate="true">phone</label>
				<editor>
					<editorType>text</editorType>
					<validation>
						<rule name="required-entry" xsi:type="boolean">false</rule>
					</validation>
				</editor>
			</settings>
		</column> -->
		<actionsColumn name="actions" class="Coditron\Lixtask\Ui\Component\Listing\Column\CustomformActions">
			<settings>
				<indexField>id</indexField>
				<resizeEnabled>false</resizeEnabled>
				<resizeDefaultWidth>107</resizeDefaultWidth>
			</settings>
		</actionsColumn>
	</columns>
</listing>
