<div class="file-uploader-summary">
    <div class="file-uploader-preview image-uploader-preview">
        <a class="image-uploader-preview-link" attr="href: $parent.getFilePreview($file)" target="_blank">
            <div class="file-uploader-spinner image-uploader-spinner" />
            <img
                class="preview-image"
                tabindex="0"
                event="load: $parent.onPreviewLoad.bind($parent)"
                attr="
                    src: $parent.getFilePreview($file),
                    alt: $file.name,
                    title: $file.name">
        </a>
 
        <div class="actions">
            <button
                type="button"
                class="action-remove"
                data-role="delete-button"
                attr="title: $t('Delete image')"
                disable="$parent.disabled"
                click="$parent.removeFile.bind($parent, $file)">
                <span translate="'Delete image'"/>
            </button>
        </div>
    </div>
 
    <div class="file-uploader-filename" text="$file.name"/>
    <div class="file-uploader-meta">
        <text args="$file.previewWidth"/>x<text args="$file.previewHeight"/>
    </div>
</div>