<?php

namespace Coditron\NotificationLogs\Ui\Component\MassAction\Users;

use Magento\Framework\UrlInterface;
use JsonSerializable;
use Coditron\NotificationLogs\Model\ResourceModel\AppNotification\CollectionFactory;

/**
 * Class UseTemplate
 */
class UseTemplate implements JsonSerializable
{
    /**
     * @var array
     */
    protected $_options;

    /**
     * @var CollectionFactory
     */
    protected $_collectionFactory;

    /**
     * Additional options params
     *
     * @var array
     */
    protected $_data;

    /**
     * @var UrlInterface
     */
    protected $_urlBuilder;

    /**
     * Base URL for subactions
     *
     * @var string
     */
    protected $_urlPath;

    /**
     * Param name for subactions
     *
     * @var string
     */
    protected $_paramName;

    /**
     * Additional params for subactions
     *
     * @var array
     */
    protected $_additionalData = [];

    /**
     * Constructor
     *
     * @param CollectionFactory $collectionFactory
     * @param UrlInterface $urlBuilder
     * @param array $data
     */
    public function __construct(
        CollectionFactory $collectionFactory,
        UrlInterface $urlBuilder,
        array $data = []
    ) {
        $this->_collectionFactory = $collectionFactory;
        $this->_urlBuilder = $urlBuilder;
        $this->_data = $data;
    }

    /**
     * Get action options
     *
     * @return array
     */
    public function jsonSerialize(): array
    {
        if ($this->_options === null) {
            $templatesColl = $this->_collectionFactory->create();

            if (!$templatesColl->getSize()) {
                // Handle empty collection
                return [
                    [
                        'value' => 'no_template',
                        'label' => __('No templates')
                    ]
                ];
            }

            $options = [];
            foreach ($templatesColl as $template) {
                $options[] = [
                    'value' => $template->getAppnotificationId(),
                    'label' => $template->getTitle()
                ];
            }

            $this->prepareData();
            $this->_options = [];

            foreach ($options as $optionCode) {
                $optionData = [
                    'type' => 'template_' . $optionCode['value'],
                    'label' => $optionCode['label']
                ];

                if ($this->_urlPath && $this->_paramName) {
                    $optionData['url'] = $this->_urlBuilder->getUrl(
                        $this->_urlPath,
                        [$this->_paramName => $optionCode['value']]
                    );
                }

                $this->_options[$optionCode['value']] = array_merge_recursive(
                    $optionData,
                    $this->_additionalData
                );
            }

            $this->_options = array_values($this->_options);
        }

        return $this->_options;
    }

    /**
     * Prepare additional data for subactions
     *
     * @return void
     */
    protected function prepareData(): void
    {
        foreach ($this->_data as $key => $value) {
            switch ($key) {
                case 'urlPath':
                    $this->_urlPath = $value;
                    break;
                case 'paramName':
                    $this->_paramName = $value;
                    break;
                default:
                    $this->_additionalData[$key] = $value;
                    break;
            }
        }
    }
}
