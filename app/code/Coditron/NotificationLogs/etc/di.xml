<?xml version="1.0" ?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
	<preference for="Coditron\NotificationLogs\Api\AppNotificationManagementInterface" type="Coditron\NotificationLogs\Model\AppNotificationManagement"/>
	<preference for="Coditron\NotificationLogs\Api\AppNotificationRepositoryInterface" type="Coditron\NotificationLogs\Model\AppNotificationRepository"/>
	<preference for="Coditron\NotificationLogs\Api\Data\AppNotificationInterface" type="Coditron\NotificationLogs\Model\AppNotification"/>
	<preference for="Coditron\NotificationLogs\Api\Data\AppNotificationSearchResultsInterface" type="Magento\Framework\Api\SearchResults"/>
	<preference for="Coditron\NotificationLogs\Api\NotificationLogsUserInterface" type="Coditron\NotificationLogs\Model\NotificationLogsUserRepository"/>
	<virtualType name="Coditron\NotificationLogs\Model\ResourceModel\AppNotification\Grid\Collection" type="Magento\Framework\View\Element\UiComponent\DataProvider\SearchResult">
		<arguments>
			<argument name="mainTable" xsi:type="string">coditron_notificationlogs_appnotification</argument>
			<argument name="resourceModel" xsi:type="string">Coditron\NotificationLogs\Model\ResourceModel\AppNotification\Collection</argument>
		</arguments>
	</virtualType>
	<type name="Magento\Framework\View\Element\UiComponent\DataProvider\CollectionFactory">
		<arguments>
			<argument name="collections" xsi:type="array">
				<item name="coditron_notificationlogs_appnotification_listing_data_source" xsi:type="string">Coditron\NotificationLogs\Model\ResourceModel\AppNotification\Grid\Collection</item>
			</argument>
		</arguments>
	</type>
	<virtualType name="Coditron\NotificationLogs\Model\ResourceModel\NotificationLogsUser\Grid\Collection" type="Magento\Framework\View\Element\UiComponent\DataProvider\SearchResult">
        <arguments>
            <argument name="mainTable" xsi:type="string">coditron_notificationlogs_user</argument>
            <argument name="resourceModel" xsi:type="string">Coditron\NotificationLogs\Model\ResourceModel\NotificationLogsUser\Collection</argument>
        </arguments>
    </virtualType>
    
    <type name="Magento\Framework\View\Element\UiComponent\DataProvider\CollectionFactory">
        <arguments>
            <argument name="collections" xsi:type="array">
                <item name="coditron_notificationlogs_registereduser_listing_data_source" xsi:type="string">Coditron\NotificationLogs\Model\ResourceModel\NotificationLogsUser\Grid\Collection</item>
            </argument>
        </arguments>
    </type>
    <type name="Coditron\NotificationLogs\Ui\Component\MassAction\Users\UseTemplate">
        <arguments>
            <argument name="data" xsi:type="array">
                <item name="urlPath" xsi:type="string">coditron_notificationlogs/registereduser/masssendnotifications</item>
                <item name="paramName" xsi:type="string">entity_id</item>
                <item name="confirm" xsi:type="array">
                    <item name="title" xsi:type="string" translatable="true">Send Notification</item>
                    <item name="message" xsi:type="string" translatable="true">Are you sure to use selected template to send notification?</item>
                </item>
            </argument>
        </arguments>
    </type>
</config>
