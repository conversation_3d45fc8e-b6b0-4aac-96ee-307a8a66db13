<?xml version="1.0" ?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Backend:etc/menu.xsd">
	<menu>
		<add id="Coditron_NotificationLogs::notifications" title="Notifications" module="Coditron_NotificationLogs" sortOrder="9999" resource="Coditron_NotificationLogs::notifications" parent="Coditron::top_level"/>
		
        <add id="Coditron_NotificationLogs::coditron_notificationlogs_appnotification" title="AppNotification" module="Coditron_NotificationLogs" sortOrder="10000" resource="Coditron_NotificationLogs::AppNotification" parent="Coditron_NotificationLogs::notifications" action="coditron_notificationlogs/appnotification/index"/>
        
        <add id="Coditron_NotificationLogs::coditron_notificationlogs_registeredusers" title="Registered Users" module="Coditron_NotificationLogs" sortOrder="10001" resource="Coditron_NotificationLogs::RegisteredUser" parent="Coditron_NotificationLogs::notifications" action="coditron_notificationlogs/registereduser/index"/>
	</menu>
</config>
