<?xml version="1.0" ?>
<schema xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:Setup/Declaration/Schema/etc/schema.xsd">
	<table name="coditron_notificationlogs_appnotification" resource="default" engine="innodb" comment="coditron_notificationlogs_appnotification Table">
		<column xsi:type="int" name="appnotification_id" padding="10" unsigned="true" nullable="false" identity="true" comment="Entity Id"/>
		<constraint xsi:type="primary" referenceId="PRIMARY">
			<column name="appnotification_id"/>
		</constraint>
		<column name="title" nullable="true" xsi:type="varchar" comment="title" length="255"/>
		<column name="message" nullable="true" xsi:type="varchar" comment="message" length="255"/>
		<column name="url" nullable="true" xsi:type="varchar" comment="url" length="255"/>
		<column name="logo" nullable="true" xsi:type="varchar" comment="logo" length="255"/>
		<column name="tags" nullable="true" xsi:type="varchar" comment="tags" length="255"/>
		<column name="created_at" nullable="true" xsi:type="timestamp" comment="created_at" default="CURRENT_TIMESTAMP"/>
		<column name="task_type" nullable="true" xsi:type="varchar" comment="task_type" length="255"/>
		<column name="body" nullable="true" xsi:type="text" comment="body"/>
	</table>

	<table name="coditron_notificationlogs_user" resource="default" engine="innodb" comment="coditron_notificationlogs_user Table">
        <column xsi:type="int" name="id" padding="10" unsigned="true" nullable="false" identity="true" comment="Entity Id"/>
        <constraint xsi:type="primary" referenceId="PRIMARY">
            <column name="id"/>
        </constraint>
        <column name="email" nullable="true" xsi:type="varchar" comment="email" length="255"/>
        <column name="customer_id" nullable="true" xsi:type="int" comment="customer_id"/>
        <column name="device_token" nullable="true" xsi:type="text" comment="device_token"/>
        <column name="created_at" nullable="true" xsi:type="timestamp" comment="created_at" default="CURRENT_TIMESTAMP"/>
    </table>
</schema>
