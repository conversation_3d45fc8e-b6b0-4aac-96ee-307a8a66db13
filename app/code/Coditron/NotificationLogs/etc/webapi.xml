<?xml version="1.0" ?>
<routes xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Webapi:etc/webapi.xsd">
	<route url="/V1/coditron-notificationlogs/appnotification" method="GET">
		<service class="Coditron\NotificationLogs\Api\AppNotificationManagementInterface" method="getAppNotification"/>
		<resources>
			<resource ref="anonymous"/>
		</resources>
	</route>
	<route url="/V1/coditron-notificationlogs/appnotification" method="POST">
		<service class="Coditron\NotificationLogs\Api\AppNotificationRepositoryInterface" method="save"/>
		<resources>
			<resource ref="Coditron_NotificationLogs::AppNotification_save"/>
		</resources>
	</route>
	<route url="/V1/coditron-notificationlogs/appnotification/search" method="GET">
		<service class="Coditron\NotificationLogs\Api\AppNotificationRepositoryInterface" method="getList"/>
		<resources>
			<resource ref="Coditron_NotificationLogs::AppNotification_view"/>
		</resources>
	</route>
	<route url="/V1/coditron-notificationlogs/appnotification/:appnotificationId" method="GET">
		<service class="Coditron\NotificationLogs\Api\AppNotificationRepositoryInterface" method="get"/>
		<resources>
			<resource ref="Coditron_NotificationLogs::AppNotification_view"/>
		</resources>
	</route>
	<route url="/V1/coditron-notificationlogs/appnotification/:appnotificationId" method="PUT">
		<service class="Coditron\NotificationLogs\Api\AppNotificationRepositoryInterface" method="save"/>
		<resources>
			<resource ref="Coditron_NotificationLogs::AppNotification_update"/>
		</resources>
	</route>
	<route url="/V1/coditron-notificationlogs/appnotification/:appnotificationId" method="DELETE">
		<service class="Coditron\NotificationLogs\Api\AppNotificationRepositoryInterface" method="deleteById"/>
		<resources>
			<resource ref="Coditron_NotificationLogs::AppNotification_delete"/>
		</resources>
	</route>
	<route url="/V1/comave-comaveapi/save-usertoken" method="POST">
        <service class="Coditron\NotificationLogs\Api\NotificationLogsUserInterface" method="saveNotificationLogsUser"/>
        <resources>
            <resource ref="self"/>
        </resources>
        <data>
            <parameter name="customerId" force="true">%customer_id%</parameter>
        </data>
    </route>
</routes>
