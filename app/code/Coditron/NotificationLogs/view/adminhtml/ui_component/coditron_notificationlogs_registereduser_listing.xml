<?xml version="1.0" ?>
<listing xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
    <argument name="data" xsi:type="array">
        <item name="js_config" xsi:type="array">
            <item name="provider" xsi:type="string">coditron_notificationlogs_registereduser_listing.coditron_notificationlogs_registereduser_listing_data_source</item>
        </item>
    </argument>
    <settings>
        <spinner>coditron_notificationlogs_registereduser_columns</spinner>
        <deps>
            <dep>coditron_notificationlogs_registereduser_listing.coditron_notificationlogs_registereduser_listing_data_source</dep>
        </deps>
    </settings>
    <dataSource name="coditron_notificationlogs_registereduser_listing_data_source" component="Magento_Ui/js/grid/provider">
        <settings>
            <storageConfig>
                <param name="indexField" xsi:type="string">id</param>
            </storageConfig>
            <updateUrl path="mui/index/render"/>
        </settings>
        <aclResource>Coditron_NotificationLogs::registereduser</aclResource>
        <dataProvider name="coditron_notificationlogs_registereduser_listing_data_source" class="Magento\Framework\View\Element\UiComponent\DataProvider\DataProvider">
            <settings>
                <requestFieldName>id</requestFieldName>
                <primaryFieldName>id</primaryFieldName>
            </settings>
        </dataProvider>
    </dataSource>
    <listingToolbar name="listing_top">
        <settings>
            <sticky>true</sticky>
        </settings>
        <bookmark name="bookmarks"/>
        <columnsControls name="columns_controls"/>
        <filters name="listing_filters"/>
        <paging name="listing_paging"/>
    </listingToolbar>
    <massaction name="listing_massaction">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="selectProvider" xsi:type="string">coditron_notificationlogs_registereduser_listing.coditron_notificationlogs_registereduser_listing.coditron_notificationlogs_registereduser_columns.ids</item>
                    <item name="component" xsi:type="string">Magento_Ui/js/grid/tree-massactions</item>
                    <item name="indexField" xsi:type="string">appnotification_id</item>
                </item>
            </argument>
            <action name="delete">
                <argument name="data" xsi:type="array">
                    <item name="config" xsi:type="array">
                        <item name="type" xsi:type="string">delete</item>
                        <item name="label" xsi:type="string" translate="true">Delete</item>
                        <item name="url" xsi:type="url" path="coditron_notificationlogs/registereduser/massDelete"/>
                        <item name="confirm" xsi:type="array">
                            <item name="title" xsi:type="string" translate="true">Delete</item>
                            <item name="message" xsi:type="string" translate="true">Do you want to delete selected row record?</item>
                        </item>
                    </item>
                </argument>
            </action>
            <action name="use_template">
                <settings>
                    <type>use_template</type>
                    <label translate="true">Send Notification</label>
                    <actions class="Coditron\NotificationLogs\Ui\Component\MassAction\Users\UseTemplate"/>
                </settings>
            </action>
            <!-- <action name="use_template">
                <argument name="data" xsi:type="array">
                    <item name="config" xsi:type="array">
                        <item name="type" xsi:type="string">use_template</item>
                        <item name="label" xsi:type="string" translate="true">Send Notification</item>
                    </item>
                </argument>
                <argument name="actions" xsi:type="configurableObject">
                    <argument name="class" xsi:type="string">Webkul\PushNotification\Ui\Component\MassAction\Users\UseTemplate</argument>
                    <argument name="data" xsi:type="array">
                        <item name="urlPath" xsi:type="string">pushnotification/manageusers/sendnotification</item>
                        <item name="paramName" xsi:type="string">entity_id</item>
                        <item name="confirm" xsi:type="array">
                            <item name="title" xsi:type="string" translate="true">Send Notification</item>
                            <item name="message" xsi:type="string" translate="true">Are you sure to use selected template to send notification?</item>
                        </item>
                    </argument>
                </argument>
            </action> -->
        </massaction>
    <columns name="coditron_notificationlogs_registereduser_columns">
         <selectionsColumn name="ids">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="indexField" xsi:type="string">id</item>
                    <item name="type" xsi:type="string">radio</item> <!-- This line sets single selection mode -->
                    <item name="sorting" xsi:type="string">desc</item>
                    <item name="sortOrder" xsi:type="number">0</item>
                </item>
            </argument>
        </selectionsColumn>
        <column name="id">
            <settings>
                <filter>text</filter>
                <sorting>asc</sorting>
                <label translate="true">ID</label>
            </settings>
        </column>
        <column name="email">
            <settings>
                <filter>text</filter>
                <label translate="true">Email</label>
            </settings>
        </column>
        <column name="customer_id">
            <settings>
                <filter>text</filter>
                <label translate="true">Customer ID</label>
            </settings>
        </column>
        <column name="device_token">
            <settings>
                <filter>text</filter>
                <label translate="true">Device Token</label>
            </settings>
        </column>
        <column name="created_at">
            <settings>
                <filter>dateRange</filter>
                <label translate="true">Created At</label>
            </settings>
        </column>
    </columns>
</listing>
