<?xml version="1.0" ?>
<listing xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
	<argument name="data" xsi:type="array">
		<item name="js_config" xsi:type="array">
			<item name="provider" xsi:type="string">coditron_notificationlogs_appnotification_listing.coditron_notificationlogs_appnotification_listing_data_source</item>
		</item>
	</argument>
	<settings>
		<spinner>coditron_notificationlogs_appnotification_columns</spinner>
		<deps>
			<dep>coditron_notificationlogs_appnotification_listing.coditron_notificationlogs_appnotification_listing_data_source</dep>
		</deps>
		<buttons>
			<button name="add">
				<url path="*/*/new"/>
				<class>primary</class>
				<label translate="true">Add New AppNotification</label>
			</button>
		</buttons>
	</settings>
	<dataSource name="coditron_notificationlogs_appnotification_listing_data_source" component="Magento_Ui/js/grid/provider">
		<settings>
			<storageConfig>
				<param name="indexField" xsi:type="string">appnotification_id</param>
			</storageConfig>
			<updateUrl path="mui/index/render"/>
		</settings>
		<aclResource>Coditron_NotificationLogs::AppNotification</aclResource>
		<dataProvider name="coditron_notificationlogs_appnotification_listing_data_source" class="Magento\Framework\View\Element\UiComponent\DataProvider\DataProvider">
			<settings>
				<requestFieldName>id</requestFieldName>
				<primaryFieldName>appnotification_id</primaryFieldName>
			</settings>
		</dataProvider>
	</dataSource>
	<listingToolbar name="listing_top">
		<settings>
			<sticky>true</sticky>
		</settings>
		<bookmark name="bookmarks"/>
		<columnsControls name="columns_controls"/>
		<filters name="listing_filters"/>
		<paging name="listing_paging"/>
	</listingToolbar>
	<columns name="coditron_notificationlogs_appnotification_columns">
		<settings>
			<editorConfig>
				<param name="selectProvider" xsi:type="string">coditron_notificationlogs_appnotification_listing.coditron_notificationlogs_appnotification_listing.coditron_notificationlogs_appnotification_columns.ids</param>
				<param name="enabled" xsi:type="boolean">true</param>
				<param name="indexField" xsi:type="string">appnotification_id</param>
				<param name="clientConfig" xsi:type="array">
					<item name="saveUrl" xsi:type="url" path="coditron_notificationlogs/AppNotification/inlineEdit"/>
					<item name="validateBeforeSave" xsi:type="boolean">false</item>
				</param>
			</editorConfig>
			<childDefaults>
				<param name="fieldAction" xsi:type="array">
					<item name="provider" xsi:type="string">coditron_notificationlogs_appnotification_listing.coditron_notificationlogs_appnotification_listing.coditron_notificationlogs_appnotification_columns_editor</item>
					<item name="target" xsi:type="string">startEdit</item>
					<item name="params" xsi:type="array">
						<item name="0" xsi:type="string">${ $.$data.rowIndex }</item>
						<item name="1" xsi:type="boolean">true</item>
					</item>
				</param>
			</childDefaults>
		</settings>
		<selectionsColumn name="ids">
			<settings>
				<indexField>appnotification_id</indexField>
			</settings>
		</selectionsColumn>
		<column name="appnotification_id">
			<settings>
				<filter>text</filter>
				<sorting>asc</sorting>
				<label translate="true">ID</label>
			</settings>
		</column>
		<column name="title">
			<settings>
				<filter>text</filter>
				<label translate="true">Title</label>
				<editor>
					<editorType>text</editorType>
					<validation>
						<rule name="required-entry" xsi:type="boolean">false</rule>
					</validation>
				</editor>
			</settings>
		</column>
		<actionsColumn name="actions" class="Coditron\NotificationLogs\Ui\Component\Listing\Column\AppNotificationActions">
			<settings>
				<indexField>appnotification_id</indexField>
				<resizeEnabled>false</resizeEnabled>
				<resizeDefaultWidth>107</resizeDefaultWidth>
			</settings>
		</actionsColumn>
		<column name="message">
			<settings>
				<filter>text</filter>
				<label translate="true">Message</label>
				<editor>
					<editorType>text</editorType>
					<validation>
						<rule name="required-entry" xsi:type="boolean">false</rule>
					</validation>
				</editor>
			</settings>
		</column>
		<column name="url">
			<settings>
				<filter>text</filter>
				<label translate="true">Url</label>
				<editor>
					<editorType>text</editorType>
					<validation>
						<rule name="required-entry" xsi:type="boolean">false</rule>
					</validation>
				</editor>
			</settings>
		</column>
		<column name="logo">
			<settings>
				<filter>text</filter>
				<label translate="true">Logo</label>
				<editor>
					<editorType>text</editorType>
					<validation>
						<rule name="required-entry" xsi:type="boolean">false</rule>
					</validation>
				</editor>
			</settings>
		</column>
		<column name="tags">
			<settings>
				<filter>text</filter>
				<label translate="true">Tags</label>
				<editor>
					<editorType>text</editorType>
					<validation>
						<rule name="required-entry" xsi:type="boolean">false</rule>
					</validation>
				</editor>
			</settings>
		</column>
		<column name="created_at">
			<settings>
				<filter>text</filter>
				<label translate="true">Created_At</label>
				<editor>
					<editorType>date</editorType>
					<validation>
						<rule name="required-entry" xsi:type="boolean">false</rule>
					</validation>
				</editor>
			</settings>
		</column>
		<column name="task_type">
			<settings>
				<filter>text</filter>
				<label translate="true">Task_Type</label>
				<editor>
					<editorType>text</editorType>
					<validation>
						<rule name="required-entry" xsi:type="boolean">false</rule>
					</validation>
				</editor>
			</settings>
		</column>
		<column name="body">
			<settings>
				<filter>text</filter>
				<label translate="true">Body</label>
				<editor>
					<editorType>text</editorType>
					<validation>
						<rule name="required-entry" xsi:type="boolean">false</rule>
					</validation>
				</editor>
			</settings>
		</column>
	</columns>
</listing>
