<?xml version="1.0" ?>
<form xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
	<argument name="data" xsi:type="array">
		<item name="js_config" xsi:type="array">
			<item name="provider" xsi:type="string">coditron_notificationlogs_appnotification_form.appnotification_form_data_source</item>
		</item>
		<item name="label" xsi:type="string" translate="true">General Information</item>
		<item name="template" xsi:type="string">templates/form/collapsible</item>
	</argument>
	<settings>
		<buttons>
			<button name="back" class="Coditron\NotificationLogs\Block\Adminhtml\AppNotification\Edit\BackButton"/>
			<button name="delete" class="Coditron\NotificationLogs\Block\Adminhtml\AppNotification\Edit\DeleteButton"/>
			<button name="save" class="Coditron\NotificationLogs\Block\Adminhtml\AppNotification\Edit\SaveButton"/>
			<button name="save_and_continue" class="Coditron\NotificationLogs\Block\Adminhtml\AppNotification\Edit\SaveAndContinueButton"/>
		</buttons>
		<namespace>coditron_notificationlogs_appnotification_form</namespace>
		<dataScope>data</dataScope>
		<deps>
			<dep>coditron_notificationlogs_appnotification_form.appnotification_form_data_source</dep>
		</deps>
	</settings>
	<dataSource name="appnotification_form_data_source">
		<argument name="data" xsi:type="array">
			<item name="js_config" xsi:type="array">
				<item name="component" xsi:type="string">Magento_Ui/js/form/provider</item>
			</item>
		</argument>
		<settings>
			<submitUrl path="*/*/save"/>
		</settings>
		<dataProvider name="appnotification_form_data_source" class="Coditron\NotificationLogs\Model\AppNotification\DataProvider">
			<settings>
				<requestFieldName>appnotification_id</requestFieldName>
				<primaryFieldName>appnotification_id</primaryFieldName>
			</settings>
		</dataProvider>
	</dataSource>
	<fieldset name="general">
		<settings>
			<label>General</label>
		</settings>
		<field name="title" formElement="input" sortOrder="10">
			<argument name="data" xsi:type="array">
				<item name="config" xsi:type="array">
					<item name="source" xsi:type="string">AppNotification</item>
				</item>
			</argument>
			<settings>
				<dataType>text</dataType>
				<label translate="true">Title</label>
				<dataScope>title</dataScope>
				<validation>
					<rule name="required-entry" xsi:type="boolean">false</rule>
				</validation>
			</settings>
		</field>
		<field name="message" formElement="input" sortOrder="20">
			<argument name="data" xsi:type="array">
				<item name="config" xsi:type="array">
					<item name="source" xsi:type="string">AppNotification</item>
				</item>
			</argument>
			<settings>
				<dataType>text</dataType>
				<label translate="true">Message</label>
				<dataScope>message</dataScope>
				<validation>
					<rule name="required-entry" xsi:type="boolean">false</rule>
				</validation>
			</settings>
		</field>
		<field name="url" formElement="input" sortOrder="30">
			<argument name="data" xsi:type="array">
				<item name="config" xsi:type="array">
					<item name="source" xsi:type="string">AppNotification</item>
				</item>
			</argument>
			<settings>
				<dataType>text</dataType>
				<label translate="true">Url</label>
				<dataScope>url</dataScope>
				<validation>
					<rule name="required-entry" xsi:type="boolean">false</rule>
				</validation>
			</settings>
		</field>
		<field name="logo" formElement="input" sortOrder="40">
			<argument name="data" xsi:type="array">
				<item name="config" xsi:type="array">
					<item name="source" xsi:type="string">AppNotification</item>
				</item>
			</argument>
			<settings>
				<dataType>text</dataType>
				<label translate="true">Logo</label>
				<dataScope>logo</dataScope>
				<validation>
					<rule name="required-entry" xsi:type="boolean">false</rule>
				</validation>
			</settings>
		</field>
		<field name="tags" formElement="input" sortOrder="50">
			<argument name="data" xsi:type="array">
				<item name="config" xsi:type="array">
					<item name="source" xsi:type="string">AppNotification</item>
				</item>
			</argument>
			<settings>
				<dataType>text</dataType>
				<label translate="true">Tags</label>
				<dataScope>tags</dataScope>
				<validation>
					<rule name="required-entry" xsi:type="boolean">false</rule>
				</validation>
			</settings>
		</field>
		<field name="created_at" formElement="date" sortOrder="60">
			<argument name="data" xsi:type="array">
				<item name="config" xsi:type="array">
					<item name="source" xsi:type="string">AppNotification</item>
				</item>
			</argument>
			<settings>
				<dataType>text</dataType>
				<label translate="true">Created_At</label>
				<dataScope>created_at</dataScope>
				<validation>
					<rule name="required-entry" xsi:type="boolean">false</rule>
				</validation>
			</settings>
		</field>
		<field name="task_type" formElement="input" sortOrder="70">
			<argument name="data" xsi:type="array">
				<item name="config" xsi:type="array">
					<item name="source" xsi:type="string">AppNotification</item>
				</item>
			</argument>
			<settings>
				<dataType>text</dataType>
				<label translate="true">Task_Type</label>
				<dataScope>task_type</dataScope>
				<validation>
					<rule name="required-entry" xsi:type="boolean">false</rule>
				</validation>
			</settings>
		</field>
		<field name="body" formElement="textarea" sortOrder="80">
			<argument name="data" xsi:type="array">
				<item name="config" xsi:type="array">
					<item name="source" xsi:type="string">AppNotification</item>
				</item>
			</argument>
			<settings>
				<dataType>text</dataType>
				<label translate="true">Body</label>
				<dataScope>body</dataScope>
				<validation>
					<rule name="required-entry" xsi:type="boolean">false</rule>
				</validation>
			</settings>
		</field>
	</fieldset>
</form>
