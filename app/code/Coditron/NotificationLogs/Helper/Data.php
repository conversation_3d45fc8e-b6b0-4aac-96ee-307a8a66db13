<?php
/**
 * @category   Webkul
 * @package    Webkul_PushNotification
 * <AUTHOR> Software Private Limited
 * @copyright  Copyright (c) Webkul Software Private Limited (https://webkul.com)
 * @license    https://store.webkul.com/license.html
 */
namespace Coditron\NotificationLogs\Helper;

use Magento\Store\Model\ScopeInterface;

/**
 * PushNotification data helper
 */
class Data extends \Magento\Framework\App\Helper\AbstractHelper
{
    private const SEND_URL_FIREFOX = 'https://updates.push.services.mozilla.com/wpush/v1/';

    private const FIREBASE_CONFIG_PATH = '/app/code/Coditron/NotificationLogs/view/frontend/web/json/firebaseconfig.json';


    /**
     * @var \Psr\Log\LoggerInterface
     */
    protected $_logger;

    /**
     * Undocumented variable
     *
     * @var [type]
     */
    protected $_storeManager;

    /**
     * @param \Magento\Framework\App\Helper\Context $context
     * @param \Magento\Framework\HTTP\Client\Curl $curl
     * @param \Magento\Framework\Encryption\EncryptorInterface $encryptor
     * @param \Magento\Framework\App\RequestInterface $httpRequest
     * @param \Magento\Store\Model\StoreManagerInterface $storeManager
     */
    public function __construct(
        \Magento\Framework\App\Helper\Context $context,
        \Magento\Framework\HTTP\Client\Curl $curl,
        \Magento\Framework\Encryption\EncryptorInterface $encryptor,
        \Magento\Framework\App\RequestInterface $httpRequest,
        \Magento\Store\Model\StoreManagerInterface $storeManager
    ) {
        $this->request = $httpRequest;
        $this->_scopeConfig = $context->getScopeConfig();
        $this->_logger = $context->getLogger();
        $this->curl = $curl;
        $this->enc = $encryptor;
        $this->_storeManager = $storeManager;
        parent::__construct($context);
    }

     /**
      * Get notificationmessage for chrome browser.
      *
      * @param int $registrationId
      * @param string $notification
      * @throws \Magento\Framework\Exception\LocalizedException
      */
    // public function sendToChrome($registrationId, $notification)
    // {
    //     $writer = new \Zend_Log_Writer_Stream(BP . '/var/log/pushNoti.log');
    //     $logger = new \Zend_Log();
    //     $logger->addWriter($writer);
    //     $logger->info('Notification');
    //     $url = 'https://fcm.googleapis.com/fcm/send';
    //     $fields = [
    //         'data' => $notification,
    //         'to' => 'fhwiILKPp0lUoU-m9Gmm0I:APA91bHRAmvfeGw0FZ2j8F471TbXS5uRGmQXRc9_7SlLlZxHaqf-OLRi-tC0O17w8bWGhtTr0vMpII9HkAvpnb7G9w2BF5jGQ3ma24TqEtDRjIdnf4fBEzR4kMiuW_V5CU2PABiNdtQ1',
    //     ];
    //     $headers = [
    //         'Authorization'=> 'key=cpvHGhJrSdeO8aTBmztyOp:APA91bE5IFzHJ6230jGr8l7Tk29L9dKs-QTdff37vsm0yMoZajsX4e5BPk9dsH5iS7CGW5ka5RDs6_w9XMqS3hJeLdM0X0l6NUAS2JT7CrexBcqEIVr2hBaB-f31HuBANByTr_2aKo1f',
    //         'Content-Type'=> 'application/json',
    //     ];
    //     $logger->info('Input Data'.print_r($fields,true));
    //     $this->curl->get($url);
    //     $this->curl->setHeaders($headers);
    //     $this->curl->post($url, json_encode($fields));
    //     $this->curl->setOption(CURLOPT_POST, true);
    //     $this->curl->setOption(CURLOPT_RETURNTRANSFER, true);
    //     $this->curl->setOption(CURLOPT_SSL_VERIFYPEER, false);
    //     $this->curl->setOption(CURLOPT_POSTFIELDS, json_encode($fields));
    //     //response will contain the output in form of JSON string
    //     $response = $this->curl->getBody();
    //     $logger->info('response'.print_r($response,true));
    //     return $response;
    // }

    private function getGoogleAccessToken(){
         $credentialsFilePath = BP . self::FIREBASE_CONFIG_PATH; //replace this with your actual path and file name
         $client = new \Google_Client();
         $client->setAuthConfig($credentialsFilePath);
         $client->addScope('https://www.googleapis.com/auth/firebase.messaging');
         $client->refreshTokenWithAssertion();
         $token = $client->getAccessToken();
         return $token['access_token'];
    }

    public function sendToChrome($registrationId, $notification)
    {   
        $access_token = $this->getGoogleAccessToken();
        //$access_token = 'ya29.c.c0AY_VpZgjrM4gxysDPmVIn5_xJXn0vIuDmhI5EO3EVHCbv57j9PIibTj67zzDsK49nQ6CrJMVPAx1ZA8API8X9vpXWmOz3-QuCOn4obmb5Uv4joTHZTx6hsgsonGMOdt8QcLpPJcMSJV36Q_O07XvRlfk1KgigVc1txQ0xvkJ8zdg5aYZukzJn58fLCQ1s4_jWFLBKoKsPCI2hufMmIt4tNhfvyGkqk-Ck9L7EtAP278q6fZU4kQXb5l4S3-wErzMYqG1FLKQh5lo05tYcLnz-mfumZR2lRNTZAUlH1UmR9y-Cfkf_opByTe-U1yQdVLR7F6R7XMKarEkDBbQWymmG8hVcgUlUFToeR0L9TAL-aF71O0e018li6FqH385PzIIah8vQR3VB25ZXQ2nVVgW2iIy-y9_cOVj0gl7_tt96MvV91UIU1xfUJi1jSsbsn3lc6q56fkgh9qc9WU19xwUhc7wOmw62URb2pll0lfujFrmbUpjSQSOU-x5l3pS4vpM3aB4YQYIUkUxiY1jyq2-BWl9UwQg-xh1fabZmeokd6dYFQ4F_R3ijB689ymc_JtIU7sRVMy_51tguhcvczSOozpYZvdVl1a5kss5XOv2vFpkVthapi5Y_62OFnsa_i-vSwd4udcBYnBRRRYBmB8VXm2Ru4mQUrtFVbvmY_FJa4nYYgrZrYQtS3_6ZgixV5jxhw3FjamVqW3VxiWQItWq1Y7tjWd6JXY85buu8fygfJB47B3qnRXvi1QdFqRBmjemz7bV6p7UxVmf78pZzu2r7yxMzctgikvgw-YYhwXbcucbbFQX-v1Igj9nrp4QOnbfrX2MdpWyR9QfMmZUJfgsSdxXqBt-zrrVd0j6Xh3rvOlXhd_Zgu4Bbaj-SfpBZrktu5Oq9IcivI4eXoj-Fjr5ImqwJ7WkUxvZ-WlMRVnIyFMXOv4pYk3dc7lQfZqOMcZ1udxhoSJI3kMt7-cosfivX9fOWVfgX28MXhzpf6kXYzjYzolSyxQkkx3';
        $apiurl = 'https://fcm.googleapis.com/v1/projects/comave-faaea/messages:send';   //replace "your-project-id" with...your project ID
        
         $headers = [
                 'Authorization: Bearer ' .$access_token,
                 'Content-Type: application/json'
         ];
        
         $notification_tray = [
                 'title'             => $notification['notification']['title'],
                 'body'           => $notification['notification']['message'],
                 // 'task_type'         => $notification['notification']['task_type'],
                 // 'body'              => $notification['notification']['body'],
             ];
        
         $in_app_module = [
                 "title"          => $notification['notification']['task_type'],
                 "body"           => $notification['notification']['body'],
             ];
         //The $in_app_module array above can be empty - I use this to send variables in to my app when it is opened, so the user sees a popup module with the message additional to the generic task tray notification.
        
          $message = [
                'message' => [
                     'token'            => $registrationId,
                     'notification'     => $notification_tray,
                     'data'             => $in_app_module,
                 ],
          ];
        
          $ch = curl_init();
          curl_setopt($ch, CURLOPT_URL, $apiurl);
          curl_setopt($ch, CURLOPT_POST, true);
          curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
          curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
          curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
          curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($message));
        
          $result = curl_exec($ch);
        
          if ($result === FALSE) {
              //Failed
              die('Curl failed: ' . curl_error($ch));
          }
        
          curl_close($ch);
    
    }


     // public function sendToChrome($registrationId, $notification){
     //    $writer = new \Zend_Log_Writer_Stream(BP . '/var/log/pushNoti.log');
     //    $logger = new \Zend_Log();
     //    $logger->addWriter($writer);
     //    $logger->info('Notification');
     //    $url = "https://fcm.googleapis.com/fcm/send";
     //        $serverKey = 'AAAAels4VoQ:APA91bFsZ4G778wZ3ZqZZcsZ7tFJjW2ucs-OkwVZbvrStMLty6a1rET7PiFs13PRECEAV3OGI9HkaQvPMKWVGnCB25QD-fQ0wsYvOFtLNULs9Es80wucoBsMyZbxYXLnf3sy3orTMsro'; // Replace with your FCM server key  
     //        $logger->info('after server Notification');
     //        $notification = [
     //            'title' => "test title",
     //            'body' => "Test body",
     //            'sound' => 'default'
     //        ];

     //        $arrayToSend = [
     //            'to' => $registrationId,
     //            'notification' => $notification,
     //            'priority' => 'high'
     //        ];

     //        $json = json_encode($arrayToSend);
     //        $logger->info('Json'.$json);
     //        $headers = [
     //            'Content-Type: application/json',
     //            'Authorization: key=' . $serverKey
     //        ];

     //        $ch = curl_init();

     //        curl_setopt($ch, CURLOPT_URL, $url);
     //        curl_setopt($ch, CURLOPT_POST, true);
     //        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
     //        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
     //        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
     //        curl_setopt($ch, CURLOPT_POSTFIELDS, $json);

     //        $response = curl_exec($ch);

     //        if ($response === FALSE) {
     //            $logger->info('In if condition');
     //            die('Curl failed: ' . curl_error($ch));
     //        }
     //        $logger->info('After if');
     //        curl_close($ch);
     //        $logger->info('response'.print_r($response,true));
     //        return $response;
     // }
    /**
     * Send to firefox
     *
     * @param  array $userIds
     * @return object
     */
    public function sendToFirefox($userIds)
    {
        try {
            $fields = "";
            $url = self::SEND_URL_FIREFOX;
            $headers = [
                'Content-Type' => 'application/json',
                'TTL' => '600000',
            ];
            $this->curl->setHeaders($headers);
            foreach ($userIds as $key => $value) {
                $finalUrl=$url.$value;
                $result = $this->curl->post($finalUrl, $fields);
                $this->_logger->info(' firefox Result ');
                $this->_logger->info(json_encode($result));
            }
            return $result;
        } catch (\Exception $e) {
            $this->_logger->info($e->getMessage());
        }
    }

    /**
     * GetStoreid function
     *
     * @return void
     */
    public function getStoreid()
    {
        return $this->_storeManager->getStore()->getId();
    }
}
