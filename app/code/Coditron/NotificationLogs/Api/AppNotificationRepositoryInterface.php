<?php
declare(strict_types=1);

namespace Coditron\NotificationLogs\Api;

use Magento\Framework\Api\SearchCriteriaInterface;

interface AppNotificationRepositoryInterface
{
    /**
     * Save AppNotification
     * @param \Coditron\NotificationLogs\Api\Data\AppNotificationInterface $appNotification
     * @return \Coditron\NotificationLogs\Api\Data\AppNotificationInterface
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function save(
        \Coditron\NotificationLogs\Api\Data\AppNotificationInterface $appNotification
    );

    /**
     * Retrieve AppNotification
     * @param string $appnotificationId
     * @return \Coditron\NotificationLogs\Api\Data\AppNotificationInterface
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function get($appnotificationId);

    /**
     * Retrieve AppNotification matching the specified criteria.
     * @param \Magento\Framework\Api\SearchCriteriaInterface $searchCriteria
     * @return \Coditron\NotificationLogs\Api\Data\AppNotificationSearchResultsInterface
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function getList(
        \Magento\Framework\Api\SearchCriteriaInterface $searchCriteria
    );

    /**
     * Delete AppNotification
     * @param \Coditron\NotificationLogs\Api\Data\AppNotificationInterface $appNotification
     * @return bool true on success
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function delete(
        \Coditron\NotificationLogs\Api\Data\AppNotificationInterface $appNotification
    );

    /**
     * Delete AppNotification by ID
     * @param string $appnotificationId
     * @return bool true on success
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function deleteById($appnotificationId);

    /**
     * Get collection by appnotificationId
     *
     * @param  int $appnotificationId
     * @return object
     */
    public function getById($appnotificationId);
}

