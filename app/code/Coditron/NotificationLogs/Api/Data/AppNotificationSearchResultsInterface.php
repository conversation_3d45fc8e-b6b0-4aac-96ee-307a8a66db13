<?php
declare(strict_types=1);

namespace Coditron\NotificationLogs\Api\Data;

interface AppNotificationSearchResultsInterface extends \Magento\Framework\Api\SearchResultsInterface
{

    /**
     * Get AppNotification list.
     * @return \Coditron\NotificationLogs\Api\Data\AppNotificationInterface[]
     */
    public function getItems();

    /**
     * Set title list.
     * @param \Coditron\NotificationLogs\Api\Data\AppNotificationInterface[] $items
     * @return $this
     */
    public function setItems(array $items);
}

