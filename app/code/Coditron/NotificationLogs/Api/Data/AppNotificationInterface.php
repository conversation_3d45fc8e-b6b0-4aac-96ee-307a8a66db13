<?php
declare(strict_types=1);

namespace Coditron\NotificationLogs\Api\Data;

interface AppNotificationInterface
{

    const MESSAGE = 'message';
    const URL = 'url';
    const LOGO = 'logo';
    const TASK_TYPE = 'task_type';
    const BODY = 'body';
    const TITLE = 'title';
    const TAGS = 'tags';
    const CREATED_AT = 'created_at';
    const APPNOTIFICATION_ID = 'appnotification_id';

    /**
     * Get appnotification_id
     * @return string|null
     */
    public function getAppnotificationId();

    /**
     * Set appnotification_id
     * @param string $appnotificationId
     * @return \Coditron\NotificationLogs\AppNotification\Api\Data\AppNotificationInterface
     */
    public function setAppnotificationId($appnotificationId);

    /**
     * Get title
     * @return string|null
     */
    public function getTitle();

    /**
     * Set title
     * @param string $title
     * @return \Coditron\NotificationLogs\AppNotification\Api\Data\AppNotificationInterface
     */
    public function setTitle($title);

    /**
     * Get message
     * @return string|null
     */
    public function getMessage();

    /**
     * Set message
     * @param string $message
     * @return \Coditron\NotificationLogs\AppNotification\Api\Data\AppNotificationInterface
     */
    public function setMessage($message);

    /**
     * Get url
     * @return string|null
     */
    public function getUrl();

    /**
     * Set url
     * @param string $url
     * @return \Coditron\NotificationLogs\AppNotification\Api\Data\AppNotificationInterface
     */
    public function setUrl($url);

    /**
     * Get logo
     * @return string|null
     */
    public function getLogo();

    /**
     * Set logo
     * @param string $logo
     * @return \Coditron\NotificationLogs\AppNotification\Api\Data\AppNotificationInterface
     */
    public function setLogo($logo);

    /**
     * Get tags
     * @return string|null
     */
    public function getTags();

    /**
     * Set tags
     * @param string $tags
     * @return \Coditron\NotificationLogs\AppNotification\Api\Data\AppNotificationInterface
     */
    public function setTags($tags);

    /**
     * Get created_at
     * @return string|null
     */
    public function getCreatedAt();

    /**
     * Set created_at
     * @param string $createdAt
     * @return \Coditron\NotificationLogs\AppNotification\Api\Data\AppNotificationInterface
     */
    public function setCreatedAt($createdAt);

    /**
     * Get task_type
     * @return string|null
     */
    public function getTaskType();

    /**
     * Set task_type
     * @param string $taskType
     * @return \Coditron\NotificationLogs\AppNotification\Api\Data\AppNotificationInterface
     */
    public function setTaskType($taskType);

    /**
     * Get body
     * @return string|null
     */
    public function getBody();

    /**
     * Set body
     * @param string $body
     * @return \Coditron\NotificationLogs\AppNotification\Api\Data\AppNotificationInterface
     */
    public function setBody($body);
}

