<?php
namespace Coditron\NotificationLogs\Api\Data;

interface NotificationLogsUserInterface
{
    /**
     * Get email
     *
     * @return string|null
     */
    public function getEmail();

    /**
     * Set email
     *
     * @param string $email
     * @return $this
     */
    public function setEmail($email);

    /**
     * Get customer ID
     *
     * @return int|null
     */
    public function getCustomerId();

    /**
     * Set customer ID
     *
     * @param int $customerId
     * @return $this
     */
    public function setCustomerId($customerId);

    /**
     * Get device token
     *
     * @return string|null
     */
    public function getDeviceToken();

    /**
     * Set device token
     *
     * @param string $deviceToken
     * @return $this
     */
    public function setDeviceToken($deviceToken);

    /**
     * Get created at
     *
     * @return string|null
     */
    public function getCreatedAt();

    /**
     * Set created at
     *
     * @param string $createdAt
     * @return $this
     */
    public function setCreatedAt($createdAt);
}
