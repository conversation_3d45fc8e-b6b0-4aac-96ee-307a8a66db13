<?php
declare(strict_types=1);

namespace Coditron\NotificationLogs\Model;

use Coditron\NotificationLogs\Api\AppNotificationRepositoryInterface;
use Coditron\NotificationLogs\Api\Data\AppNotificationInterface;
use Coditron\NotificationLogs\Api\Data\AppNotificationInterfaceFactory;
use Coditron\NotificationLogs\Api\Data\AppNotificationSearchResultsInterfaceFactory;
use Coditron\NotificationLogs\Helper\Data;
use Coditron\NotificationLogs\Model\ResourceModel\AppNotification as ResourceAppNotification;
use Coditron\NotificationLogs\Model\ResourceModel\AppNotification\CollectionFactory as AppNotificationCollectionFactory;
use Magento\Framework\Api\SearchCriteria\CollectionProcessorInterface;
use Magento\Framework\Api\SearchCriteriaInterface;
use Magento\Framework\Exception\CouldNotDeleteException;
use Magento\Framework\Exception\CouldNotSaveException;
use Magento\Framework\Exception\NoSuchEntityException;
use Coditron\NotificationLogs\Model\ResourceModel\NotificationLogsUser\CollectionFactory;

class AppNotificationRepository implements AppNotificationRepositoryInterface
{
    public function __construct(
        private readonly ResourceAppNotification $resource,
        private readonly AppNotificationInterfaceFactory $appNotificationFactory,
        private readonly AppNotificationCollectionFactory $appNotificationCollectionFactory,
        private readonly AppNotificationSearchResultsInterfaceFactory $searchResultsFactory,
        private CollectionProcessorInterface $collectionProcessor,
        private readonly CollectionFactory $collectionFactory,
        private readonly Data $helper
    ) {
    }

    /**
     * @inheritDoc
     */
    public function save(
        AppNotificationInterface $appNotification
    ) {
        $writer = new \Zend_Log_Writer_Stream(BP . '/var/log/pushNoti.log');
        $logger = new \Zend_Log();
        $logger->addWriter($writer);

        try {
            $this->resource->save($appNotification);
        } catch (\Exception $exception) {
            throw new CouldNotSaveException(__(
                'Could not save the appNotification: %1',
                $exception->getMessage()
            ));
        }
        $templateId = $appNotification['appnotification_id'];
        $bodyData = json_decode($appNotification['body'], true);
        $userId = null;
        if ($bodyData !== null && isset($bodyData['user_id'])) {
            $userId = $bodyData['user_id'];
        }
        if ($userId !== null) {
            $logger->info('in if');
            $collections = $this->collectionFactory->create();

            // Add a filter to fetch records for the specific user_id
            $collections->addFieldToFilter('customer_id', $userId);

            // Assuming 'device_token' is the field name in your database
            //$deviceToken = null;
            foreach($collections as $collection)
            {
                $deviceToken = $collection->getData('device_token');
                $notificationData = [];
                $notification = [];
                $notificationData['title'] =  $appNotification['title'];
                $notificationData['message'] = $appNotification['message'];
                $notificationData['task_type'] =  $appNotification['task_type'];
                $notificationData['body'] = $appNotification['body'];
                $notificationData['actions'][0]['action'] = $appNotification['url'];
                $notificationData['actions'][0]['title'] = $appNotification['title'];
                $notification['notification'] = $notificationData;
                $this->helper->sendToChrome($deviceToken, $notification);
            }
            // if ($collection->getSize()) {
            //     $deviceToken = $collection->getFirstItem()->getData('device_token');
            // }
            // $notificationData = [];
            // $notification = [];
            // $notificationData['title'] =  $appNotification['title'];
            // $notificationData['message'] = $appNotification['message'];
            // $notificationData['task_type'] =  $appNotification['task_type'];
            // $notificationData['body'] = $appNotification['body'];
            // $notificationData['actions'][0]['action'] = $appNotification['url'];
            // $notificationData['actions'][0]['title'] = $appNotification['title'];
            // $notification['notification'] = $notificationData;
            // $this->_helper->sendToChrome($deviceToken, $notification);
        }
        else{
            $logger->info('in else');
            $collections = $this->collectionFactory->create();
            foreach($collections as $collection)
            {
                $deviceToken = $collection->getData('device_token');
                $notificationData = [];
                $notification = [];
                $notificationData['title'] =  $appNotification['title'];
                $notificationData['message'] = $appNotification['message'];
                $notificationData['task_type'] =  $appNotification['task_type'];
                $notificationData['body'] = $appNotification['body'];
                $notificationData['actions'][0]['action'] = $appNotification['url'];
                $notificationData['actions'][0]['title'] = $appNotification['title'];
                $notification['notification'] = $notificationData;
                $this->helper->sendToChrome($deviceToken, $notification);
            }
        }
        return $appNotification;
    }

    /**
     * @inheritDoc
     */
    public function get($appNotificationId)
    {
        $appNotification = $this->appNotificationFactory->create();
        $this->resource->load($appNotification, $appNotificationId);
        if (!$appNotification->getId()) {
            throw new NoSuchEntityException(__('AppNotification with id "%1" does not exist.', $appNotificationId));
        }
        return $appNotification;
    }

    /**
     * @inheritDoc
     */
    public function getList(
        SearchCriteriaInterface $criteria
    ) {
        $collection = $this->appNotificationCollectionFactory->create();

        $this->collectionProcessor->process($criteria, $collection);

        $searchResults = $this->searchResultsFactory->create();
        $searchResults->setSearchCriteria($criteria);

        $items = [];
        foreach ($collection as $model) {
            $items[] = $model;
        }

        $searchResults->setItems($items);
        $searchResults->setTotalCount($collection->getSize());
        return $searchResults;
    }

    /**
     * @inheritDoc
     */
    public function delete(
        AppNotificationInterface $appNotification
    ) {
        try {
            $appNotificationModel = $this->appNotificationFactory->create();
            $this->resource->load($appNotificationModel, $appNotification->getAppnotificationId());
            $this->resource->delete($appNotificationModel);
        } catch (\Exception $exception) {
            throw new CouldNotDeleteException(__(
                'Could not delete the AppNotification: %1',
                $exception->getMessage()
            ));
        }
        return true;
    }

    /**
     * @inheritDoc
     */
    public function deleteById($appNotificationId)
    {
        return $this->delete($this->get($appNotificationId));
    }

     /**
     * Get collection by template id
     *
     * @param  int $templateId
     * @return object
     */
    public function getById($appNotificationId)
    {
        $appNotification = $this->appNotificationFactory->create();
        $this->resource->load($appNotification, $appNotificationId);
        return $appNotification;
    }
}
