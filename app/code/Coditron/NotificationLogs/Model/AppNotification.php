<?php
declare(strict_types=1);

namespace Coditron\NotificationLogs\Model;

use Coditron\NotificationLogs\Api\Data\AppNotificationInterface;
use Magento\Framework\Model\AbstractModel;

class AppNotification extends AbstractModel implements AppNotificationInterface
{

    /**
     * @inheritDoc
     */
    public function _construct()
    {
        $this->_init(\Coditron\NotificationLogs\Model\ResourceModel\AppNotification::class);
    }

    /**
     * @inheritDoc
     */
    public function getAppnotificationId()
    {
        return $this->getData(self::APPNOTIFICATION_ID);
    }

    /**
     * @inheritDoc
     */
    public function setAppnotificationId($appnotificationId)
    {
        return $this->setData(self::APPNOTIFICATION_ID, $appnotificationId);
    }

    /**
     * @inheritDoc
     */
    public function getTitle()
    {
        return $this->getData(self::TITLE);
    }

    /**
     * @inheritDoc
     */
    public function setTitle($title)
    {
        return $this->setData(self::TITLE, $title);
    }

    /**
     * @inheritDoc
     */
    public function getMessage()
    {
        return $this->getData(self::MESSAGE);
    }

    /**
     * @inheritDoc
     */
    public function setMessage($message)
    {
        return $this->setData(self::MESSAGE, $message);
    }

    /**
     * @inheritDoc
     */
    public function getUrl()
    {
        return $this->getData(self::URL);
    }

    /**
     * @inheritDoc
     */
    public function setUrl($url)
    {
        return $this->setData(self::URL, $url);
    }

    /**
     * @inheritDoc
     */
    public function getLogo()
    {
        return $this->getData(self::LOGO);
    }

    /**
     * @inheritDoc
     */
    public function setLogo($logo)
    {
        return $this->setData(self::LOGO, $logo);
    }

    /**
     * @inheritDoc
     */
    public function getTags()
    {
        return $this->getData(self::TAGS);
    }

    /**
     * @inheritDoc
     */
    public function setTags($tags)
    {
        return $this->setData(self::TAGS, $tags);
    }

    /**
     * @inheritDoc
     */
    public function getCreatedAt()
    {
        return $this->getData(self::CREATED_AT);
    }

    /**
     * @inheritDoc
     */
    public function setCreatedAt($createdAt)
    {
        return $this->setData(self::CREATED_AT, $createdAt);
    }

    /**
     * @inheritDoc
     */
    public function getTaskType()
    {
        return $this->getData(self::TASK_TYPE);
    }

    /**
     * @inheritDoc
     */
    public function setTaskType($taskType)
    {
        return $this->setData(self::TASK_TYPE, $taskType);
    }

    /**
     * @inheritDoc
     */
    public function getBody()
    {
        return $this->getData(self::BODY);
    }

    /**
     * @inheritDoc
     */
    public function setBody($body)
    {
        return $this->setData(self::BODY, $body);
    }
}

