<?php
namespace Coditron\NotificationLogs\Model;

use AllowDynamicProperties;
use Coditron\NotificationLogs\Api\NotificationLogsUserInterface;
use Coditron\NotificationLogs\Model\ResourceModel\NotificationLogsUser as ResourceNotificationLogsUser;
use Coditron\NotificationLogs\Model\NotificationLogsUserFactory;
use Coditron\NotificationLogs\Model\ResourceModel\NotificationLogsUser\CollectionFactory;
use Magento\Customer\Api\CustomerRepositoryInterface;

#[AllowDynamicProperties]
class NotificationLogsUserRepository implements NotificationLogsUserInterface
{
    protected $resource;
    protected $collectionFactory;

    public function __construct(
        ResourceNotificationLogsUser $resource,
        NotificationLogsUserFactory $notificationLogsUserFactory,
        CustomerRepositoryInterface $customerRepository,
        CollectionFactory $collectionFactory
    )
    {
        $this->resource = $resource;
        $this->notificationLogsUserFactory = $notificationLogsUserFactory;
        $this->customerRepository = $customerRepository;
        $this->collectionFactory = $collectionFactory;
    }

    public function saveNotificationLogsUser($customerId, $device_token)
    {
        try {
            $customer = $this->customerRepository->getById($customerId);
            $email = $customer->getEmail();
            $lixid = $customer->getCustomAttribute('lix_uid');
            if($lixid != null) {
                $lixuid = $lixid->getValue();
                $collection = $this->collectionFactory->create()
                ->addFieldToFilter('device_token', $device_token);
                $tokens = $collection->getItems();

                $deviceTokenExists = false;
                $customerExistsForToken = false;

                foreach ($tokens as $token) {
                    if ($token->getDeviceToken() == $device_token) {
                        $deviceTokenExists = true;
                        if ($token->getCustomerId() == $lixuid) {
                            $customerExistsForToken = true;
                            break;
                        }
                    }
                }

                if ($deviceTokenExists && !$customerExistsForToken) {
                    $notificationLogsUser = $this->notificationLogsUserFactory->create();
                    $notificationLogsUser->setEmail($email);
                    $notificationLogsUser->setCustomerId($lixuid);
                    $notificationLogsUser->setDeviceToken($device_token);
                    $notificationLogsUser->save();
                } elseif (!$deviceTokenExists) {
                    $notificationLogsUser = $this->notificationLogsUserFactory->create();
                    $notificationLogsUser->setEmail($email);
                    $notificationLogsUser->setCustomerId($lixuid);
                    $notificationLogsUser->setDeviceToken($device_token);
                    $notificationLogsUser->save();
                }
                else
                {
                     return false;
                }
                return true;
            }
            else{
                return false;
            }
        } catch (\Exception $exception) {
            return false;
        }
    }
}
