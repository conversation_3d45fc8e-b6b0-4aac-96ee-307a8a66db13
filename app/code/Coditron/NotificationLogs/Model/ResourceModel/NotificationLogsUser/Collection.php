<?php
declare(strict_types=1);

namespace Coditron\NotificationLogs\Model\ResourceModel\NotificationLogsUser;

use Magento\Framework\Model\ResourceModel\Db\Collection\AbstractCollection;

class Collection extends AbstractCollection
{

    /**
     * @inheritDoc
     */
    protected $_idFieldName = 'id';

    /**
     * @inheritDoc
     */
    protected function _construct()
    {
        $this->_init(
            \Coditron\NotificationLogs\Model\NotificationLogsUser::class,
            \Coditron\NotificationLogs\Model\ResourceModel\NotificationLogsUser::class
        );
    }
}

