<?php
declare(strict_types=1);

namespace Coditron\NotificationLogs\Model\ResourceModel\AppNotification;

use Magento\Framework\Model\ResourceModel\Db\Collection\AbstractCollection;

class Collection extends AbstractCollection
{

    /**
     * @inheritDoc
     */
    protected $_idFieldName = 'appnotification_id';

    /**
     * @inheritDoc
     */
    protected function _construct()
    {
        $this->_init(
            \Coditron\NotificationLogs\Model\AppNotification::class,
            \Coditron\NotificationLogs\Model\ResourceModel\AppNotification::class
        );
    }
}

