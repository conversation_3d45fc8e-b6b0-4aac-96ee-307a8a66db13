<?php
namespace Coditron\NotificationLogs\Model;

use Magento\Framework\Model\AbstractModel;
use Coditron\NotificationLogs\Api\Data\NotificationLogsUserInterface as DataNotificationLogsUserInterface;

class NotificationLogsUser extends AbstractModel implements DataNotificationLogsUserInterface
{
    protected function _construct()
    {
        $this->_init(\Coditron\NotificationLogs\Model\ResourceModel\NotificationLogsUser::class);
    }

    public function getEmail()
    {
        return $this->getData('email');
    }

    public function setEmail($email)
    {
        return $this->setData('email', $email);
    }

    public function getCustomerId()
    {
        return $this->getData('customer_id');
    }

    public function setCustomerId($customerId)
    {
        return $this->setData('customer_id', $customerId);
    }

    public function getDeviceToken()
    {
        return $this->getData('device_token');
    }

    public function setDeviceToken($deviceToken)
    {
        return $this->setData('device_token', $deviceToken);
    }

    public function getCreatedAt()
    {
        return $this->getData('created_at');
    }

    public function setCreatedAt($createdAt)
    {
        return $this->setData('created_at', $createdAt);
    }
}
