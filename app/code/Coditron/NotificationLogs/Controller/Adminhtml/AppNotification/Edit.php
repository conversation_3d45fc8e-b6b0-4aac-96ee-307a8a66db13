<?php
declare(strict_types=1);

namespace Coditron\NotificationLogs\Controller\Adminhtml\AppNotification;

class Edit extends \Coditron\NotificationLogs\Controller\Adminhtml\AppNotification
{

    protected $resultPageFactory;

    /**
     * @param \Magento\Backend\App\Action\Context $context
     * @param \Magento\Framework\Registry $coreRegistry
     * @param \Magento\Framework\View\Result\PageFactory $resultPageFactory
     */
    public function __construct(
        \Magento\Backend\App\Action\Context $context,
        \Magento\Framework\Registry $coreRegistry,
        \Magento\Framework\View\Result\PageFactory $resultPageFactory
    ) {
        $this->resultPageFactory = $resultPageFactory;
        parent::__construct($context, $coreRegistry);
    }

    /**
     * Edit action
     *
     * @return \Magento\Framework\Controller\ResultInterface
     */
    public function execute()
    {
        // 1. Get ID and create model
        $id = $this->getRequest()->getParam('appnotification_id');
        $model = $this->_objectManager->create(\Coditron\NotificationLogs\Model\AppNotification::class);
        
        // 2. Initial checking
        if ($id) {
            $model->load($id);
            if (!$model->getId()) {
                $this->messageManager->addErrorMessage(__('This Appnotification no longer exists.'));
                /** @var \Magento\Backend\Model\View\Result\Redirect $resultRedirect */
                $resultRedirect = $this->resultRedirectFactory->create();
                return $resultRedirect->setPath('*/*/');
            }
        }
        $this->_coreRegistry->register('coditron_notificationlogs_appnotification', $model);
        
        // 3. Build edit form
        /** @var \Magento\Backend\Model\View\Result\Page $resultPage */
        $resultPage = $this->resultPageFactory->create();
        $this->initPage($resultPage)->addBreadcrumb(
            $id ? __('Edit Appnotification') : __('New AppNotification'),
            $id ? __('Edit Appnotification') : __('New AppNotification')
        );
        $resultPage->getConfig()->getTitle()->prepend(__('Appnotifications'));
        $resultPage->getConfig()->getTitle()->prepend($model->getId() ? __('Edit Appnotification %1', $model->getId()) : __('New AppNotification'));
        return $resultPage;
    }
}

