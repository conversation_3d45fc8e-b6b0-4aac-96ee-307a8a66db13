<?php
declare(strict_types=1);

namespace Coditron\NotificationLogs\Controller\Adminhtml;

use Magento\Backend\App\Action\Context;

abstract class Registereduser extends \Magento\Backend\App\Action
{

    protected $_coreRegistry;
    const ADMIN_RESOURCE = 'Coditron_NotificationLogs::top_level';

    /**
     * @param \Magento\Backend\App\Action\Context $context
     * @param \Magento\Framework\Registry $coreRegistry
     */
    public function __construct(
        \Magento\Backend\App\Action\Context $context,
        \Magento\Framework\Registry $coreRegistry
    ) {
        $this->_coreRegistry = $coreRegistry;
        parent::__construct($context);
    }

     /**
     * Manage Users
     *
     * @return boolean
     */
    public function execute()
    {
        return 1;
    }
    
    /**
     * Check for is allowed
     *
     * @return boolean
     */
    protected function _isAllowed()
    {
        return $this->_authorization
                    ->isAllowed(
                        'Coditron_NotificationLogs::Registereduser'
                    );
    }

    /**
     * Init page
     *
     * @param \Magento\Backend\Model\View\Result\Page $resultPage
     * @return \Magento\Backend\Model\View\Result\Page
     */
    public function initPage($resultPage)
    {
        $resultPage->setActiveMenu(self::ADMIN_RESOURCE)
            ->addBreadcrumb(__('Coditron'), __('Coditron'))
            ->addBreadcrumb(__('Registereduser'), __('Registereduser'));
        return $resultPage;
    }
}

