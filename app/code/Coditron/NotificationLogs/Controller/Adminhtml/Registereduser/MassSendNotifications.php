<?php
declare(strict_types=1);

namespace Coditron\NotificationLogs\Controller\Adminhtml\Registereduser;

use Magento\Backend\App\Action;
use Magento\Framework\Controller\ResultFactory;
use Magento\Backend\App\Action\Context;
use Magento\Framework\View\Result\PageFactory;
use Magento\Ui\Component\MassAction\Filter;
use Coditron\NotificationLogs\Controller\Adminhtml\Registereduser;
use Coditron\NotificationLogs\Model\ResourceModel\NotificationLogsUser\CollectionFactory;
use Coditron\NotificationLogs\Api\AppNotificationRepositoryInterface;
use Coditron\NotificationLogs\Model\NotificationLogsUser;
use Magento\Store\Model\StoreManagerInterface;
use Magento\Framework\Registry;

class MassSendNotifications extends Registereduser
{
    protected $notificationLogsUserFactory;

    public function __construct(
        Action\Context $context,
        \Coditron\NotificationLogs\Model\NotificationLogsUserFactory $notificationLogsUserFactory,
        Filter $filter,
        PageFactory $resultPageFactory,
        CollectionFactory $collectionFactory,
        AppNotificationRepositoryInterface $templatesRepository,
        StoreManagerInterface $storemanager,
        \Coditron\NotificationLogs\Helper\Data $helper,
        NotificationLogsUser $usersToken,
        Registry $coreRegistry
    ) {
        $this->notificationLogsUserFactory = $notificationLogsUserFactory;
        $this->_filter = $filter;
        parent::__construct($context, $coreRegistry);
        $this->_resultPageFactory = $resultPageFactory;
        $this->_collectionFactory = $collectionFactory;
        $this->_templatesRepository = $templatesRepository;
        $this->_storeManager = $storemanager;
        $this->_helper = $helper;
        $this->_usersToken = $usersToken;
    }

    public function execute()
    {
        $writer = new \Zend_Log_Writer_Stream(BP . '/var/log/masssendNoti.log');
        $logger = new \Zend_Log();
        $logger->addWriter($writer);
        $logger->info('In mass sendNotification');
        $templateId = $this->getRequest()->getParam('entity_id');
        $logger->info('templateId'.print_r($templateId,true));
        $model = $this->_filter;
        $collection = $model->getCollection($this->_collectionFactory->create());

        $response = $this->notificationData($templateId, $collection);
        $logger->info('response= '.print_r($response,true));
        $resultRedirect = $this->resultRedirectFactory->create();

        return $resultRedirect->setPath('*/*/index');
        // $ids = $this->getRequest()->getParam('selected');
        // if (!is_array($ids) || empty($ids)) {
        //     $this->messageManager->addErrorMessage(__('Please select user(s).'));
        // } else {
        //     try {
        //         // Add your notification sending logic here
        //         foreach ($ids as $id) {
        //             $model = $this->notificationLogsUserFactory->create()->load($id);
        //             // Send notification logic
        //         }
        //         $this->messageManager->addSuccessMessage(__('Notifications have been sent to %1 user(s).', count($ids)));
        //     } catch (\Exception $e) {
        //         $this->messageManager->addErrorMessage($e->getMessage());
        //     }
        // }
        // return $this->resultFactory->create(ResultFactory::TYPE_REDIRECT)->setPath('*/*/index');
    }

    /**
     * Manage notification data
     *
     * @param  int $templateId
     * @param  object $collection
     * @return int|null
     */
    protected function notificationData($templateId, $collection)
    {
        $writer = new \Zend_Log_Writer_Stream(BP . '/var/log/masssendNoti.log');
        $logger = new \Zend_Log();
        $logger->addWriter($writer);
        $logger->info('In mass notificationData');
        $chrome = [];
        $mozila = [];

        $templateData = $this->_templatesRepository->getById($templateId);
        $mediaUrl = $this->_storeManager
                            ->getStore()
                            ->getBaseUrl(
                                \Magento\Framework\UrlInterface::URL_TYPE_MEDIA
                            );
        $logo = $mediaUrl.'pushnotification/'.$templateData->getLogo();
        $logger->info('logo'.print_r($logo,true));
        $notificationData = [];
        $notification = [];
        $notificationData['title'] =  $templateData->getTitle();
        $notificationData['message'] = $templateData->getMessage();
        $notificationData['task_type'] =  $templateData->getTaskType();
        $notificationData['body'] = $templateData->getBody();
        $notificationData['actions'][0]['action'] = $templateData->getUrl();
        $notificationData['actions'][0]['title'] = $templateData->getTitle();
        $notificationData['icon'] = $logo;
        $notification['notification'] = $notificationData;
        $logger->info('notificationData'.print_r($notificationData,true));
        foreach ($collection as $key => $user) {
            $user->setTemplateId($templateId)->save();
            $logger->info('In device token'.print_r($user->getDeviceToken(),true));
            $logger->info('In notification data= '.print_r($notification,true));
            $response = $this->_helper->sendToChrome($user->getDeviceToken(), $notification);
            //$logger->info('In response'.print_r($response,true));
            // if ($user->getBrowser() == self::CHROME) {
            //     $response = $this->_helper->sendToChrome($user->getToken(), $notification);
            // } elseif ($user->getBrowser() == self::FIREFOX) {
            //     $response = $this->_helper->sendToFirefox($user->getToken(), $notification);
            // }
        }
        $this->messageManager->addSuccess(
            __('Push notification(s) has been sent')
        );
    }
}

