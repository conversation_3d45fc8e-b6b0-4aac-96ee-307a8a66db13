<?php
declare(strict_types=1);

namespace Coditron\UserHistory\Controller\Adminhtml\CoditronUserscanHistory;

class Delete extends \Coditron\UserHistory\Controller\Adminhtml\CoditronUserscanHistory
{

    /**
     * Delete action
     *
     * @return \Magento\Framework\Controller\ResultInterface
     */
    public function execute()
    {
        /** @var \Magento\Backend\Model\View\Result\Redirect $resultRedirect */
        $resultRedirect = $this->resultRedirectFactory->create();
        // check if we know what should be deleted
        $id = $this->getRequest()->getParam('id');
        if ($id) {
            try {
                // init model and delete
                $model = $this->_objectManager->create(\Coditron\UserHistory\Model\CoditronUserscanHistory::class);
                $model->load($id);
                $model->delete();
                // display success message
                $this->messageManager->addSuccessMessage(__('You deleted the Coditron Userscanhistory.'));
                // go to grid
                return $resultRedirect->setPath('*/*/');
            } catch (\Exception $e) {
                // display error message
                $this->messageManager->addErrorMessage($e->getMessage());
                // go back to edit form
                return $resultRedirect->setPath('*/*/edit', ['id' => $id]);
            }
        }
        // display error message
        $this->messageManager->addErrorMessage(__('We can\'t find a Coditron Userscanhistory to delete.'));
        // go to grid
        return $resultRedirect->setPath('*/*/');
    }
}

