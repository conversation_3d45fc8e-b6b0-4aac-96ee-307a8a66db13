<?xml version="1.0" ?>
<schema xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:Setup/Declaration/Schema/etc/schema.xsd">
	<table name="coditron_userscanhistory" resource="default" engine="innodb" comment="coditron_userscanhistory Table">
		<column xsi:type="int" name="id" padding="10" unsigned="true" nullable="false" identity="true" comment="Entity Id"/>
		<constraint xsi:type="primary" referenceId="PRIMARY">
			<column name="id"/>
		</constraint>
		<column name="user_id" nullable="true" xsi:type="int" comment="user_id" identity="false"/>
		<column name="seller_id" nullable="true" xsi:type="text" comment="seller_id"/>
		<column name="count" nullable="true" xsi:type="int" comment="count" identity="false"/>
		<column name="scan_at" nullable="true" xsi:type="timestamp" comment="scan_at"/>
	</table>
</schema>
