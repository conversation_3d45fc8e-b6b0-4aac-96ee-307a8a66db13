<?xml version="1.0" ?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
	<preference for="Coditron\UserHistory\Api\CoditronUserscanHistoryRepositoryInterface" type="Coditron\UserHistory\Model\CoditronUserscanHistoryRepository"/>
	<preference for="Coditron\UserHistory\Api\Data\CoditronUserscanHistoryInterface" type="Coditron\UserHistory\Model\CoditronUserscanHistory"/>
	<preference for="Coditron\UserHistory\Api\Data\CoditronUserscanHistorySearchResultsInterface" type="Magento\Framework\Api\SearchResults"/>
	<virtualType name="Coditron\UserHistory\Model\ResourceModel\CoditronUserscanHistory\Grid\Collection" type="Magento\Framework\View\Element\UiComponent\DataProvider\SearchResult">
		<arguments>
			<argument name="mainTable" xsi:type="string">coditron_userscanhistory</argument>
			<argument name="resourceModel" xsi:type="string">Coditron\UserHistory\Model\ResourceModel\CoditronUserscanHistory\Collection</argument>
		</arguments>
	</virtualType>
	<type name="Magento\Framework\View\Element\UiComponent\DataProvider\CollectionFactory">
		<arguments>
			<argument name="collections" xsi:type="array">
				<item name="coditron_userhistory_coditron_userscanhistory_listing_data_source" xsi:type="string">Coditron\UserHistory\Model\ResourceModel\CoditronUserscanHistory\Grid\Collection</item>
			</argument>
		</arguments>
	</type>
	<preference for="Coditron\UserHistory\Api\UserscanhistoryInterface" type="Coditron\UserHistory\Model\UserscanhistoryRepository"/>
</config>
