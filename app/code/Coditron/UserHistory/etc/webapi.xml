<?xml version="1.0" ?>
<routes xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Webapi:etc/webapi.xsd">
	<route url="/V1/coditron-userhistory/coditron_userscanhistory" method="POST">
		<service class="Coditron\UserHistory\Api\CoditronUserscanHistoryRepositoryInterface" method="save"/>
		<resources>
			<resource ref="Coditron_UserHistory::Coditron_UserscanHistory_save"/>
		</resources>
	</route>
	<route url="/V1/coditron-userhistory/coditron_userscanhistory/search" method="GET">
		<service class="Coditron\UserHistory\Api\CoditronUserscanHistoryRepositoryInterface" method="getList"/>
		<resources>
			<resource ref="Coditron_UserHistory::Coditron_UserscanHistory_view"/>
		</resources>
	</route>
	<route url="/V1/coditron-userhistory/coditron_userscanhistory/:coditronUserscanhistoryId" method="GET">
		<service class="Coditron\UserHistory\Api\CoditronUserscanHistoryRepositoryInterface" method="get"/>
		<resources>
			<resource ref="Coditron_UserHistory::Coditron_UserscanHistory_view"/>
		</resources>
	</route>
	<route url="/V1/coditron-userhistory/coditron_userscanhistory/:coditronUserscanhistoryId" method="PUT">
		<service class="Coditron\UserHistory\Api\CoditronUserscanHistoryRepositoryInterface" method="save"/>
		<resources>
			<resource ref="Coditron_UserHistory::Coditron_UserscanHistory_update"/>
		</resources>
	</route>
	<route url="/V1/coditron-userhistory/coditron_userscanhistory/:coditronUserscanhistoryId" method="DELETE">
		<service class="Coditron\UserHistory\Api\CoditronUserscanHistoryRepositoryInterface" method="deleteById"/>
		<resources>
			<resource ref="Coditron_UserHistory::Coditron_UserscanHistory_delete"/>
		</resources>
	</route>
	<route url="/V1/comave-comaveapi/scanhistory" method="POST">
        <service class="Coditron\UserHistory\Api\UserscanhistoryInterface" method="updateScanHistory"/>
        <resources>
			<resource ref="self"/>
		</resources>
		<data>
  			<parameter name="customerId" force="true">%customer_id%</parameter>
 		</data>
    </route>
</routes>
