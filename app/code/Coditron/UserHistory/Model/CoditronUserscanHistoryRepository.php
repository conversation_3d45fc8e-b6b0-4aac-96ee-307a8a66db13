<?php
declare(strict_types=1);

namespace Coditron\UserHistory\Model;

use Coditron\UserHistory\Api\CoditronUserscanHistoryRepositoryInterface;
use Coditron\UserHistory\Api\Data\CoditronUserscanHistoryInterface;
use Coditron\UserHistory\Api\Data\CoditronUserscanHistoryInterfaceFactory;
use Coditron\UserHistory\Api\Data\CoditronUserscanHistorySearchResultsInterfaceFactory;
use Coditron\UserHistory\Model\ResourceModel\CoditronUserscanHistory as ResourceCoditronUserscanHistory;
use Coditron\UserHistory\Model\ResourceModel\CoditronUserscanHistory\CollectionFactory as CoditronUserscanHistoryCollectionFactory;
use Magento\Framework\Api\SearchCriteria\CollectionProcessorInterface;
use Magento\Framework\Exception\CouldNotDeleteException;
use Magento\Framework\Exception\CouldNotSaveException;
use Magento\Framework\Exception\NoSuchEntityException;

class CoditronUserscanHistoryRepository implements CoditronUserscanHistoryRepositoryInterface
{

    /**
     * @var CollectionProcessorInterface
     */
    protected $collectionProcessor;

    /**
     * @var CoditronUserscanHistory
     */
    protected $searchResultsFactory;

    /**
     * @var ResourceCoditronUserscanHistory
     */
    protected $resource;

    /**
     * @var CoditronUserscanHistoryCollectionFactory
     */
    protected $coditronUserscanHistoryCollectionFactory;

    /**
     * @var CoditronUserscanHistoryInterfaceFactory
     */
    protected $coditronUserscanHistoryFactory;


    /**
     * @param ResourceCoditronUserscanHistory $resource
     * @param CoditronUserscanHistoryInterfaceFactory $coditronUserscanHistoryFactory
     * @param CoditronUserscanHistoryCollectionFactory $coditronUserscanHistoryCollectionFactory
     * @param CoditronUserscanHistorySearchResultsInterfaceFactory $searchResultsFactory
     * @param CollectionProcessorInterface $collectionProcessor
     */
    public function __construct(
        ResourceCoditronUserscanHistory $resource,
        CoditronUserscanHistoryInterfaceFactory $coditronUserscanHistoryFactory,
        CoditronUserscanHistoryCollectionFactory $coditronUserscanHistoryCollectionFactory,
        CoditronUserscanHistorySearchResultsInterfaceFactory $searchResultsFactory,
        CollectionProcessorInterface $collectionProcessor
    ) {
        $this->resource = $resource;
        $this->coditronUserscanHistoryFactory = $coditronUserscanHistoryFactory;
        $this->coditronUserscanHistoryCollectionFactory = $coditronUserscanHistoryCollectionFactory;
        $this->searchResultsFactory = $searchResultsFactory;
        $this->collectionProcessor = $collectionProcessor;
    }

    /**
     * @inheritDoc
     */
    public function save(
        CoditronUserscanHistoryInterface $coditronUserscanHistory
    ) {
        try {
            $this->resource->save($coditronUserscanHistory);
        } catch (\Exception $exception) {
            throw new CouldNotSaveException(__(
                'Could not save the coditronUserscanHistory: %1',
                $exception->getMessage()
            ));
        }
        return $coditronUserscanHistory;
    }

    /**
     * @inheritDoc
     */
    public function get($coditronUserscanHistoryId)
    {
        $coditronUserscanHistory = $this->coditronUserscanHistoryFactory->create();
        $this->resource->load($coditronUserscanHistory, $coditronUserscanHistoryId);
        if (!$coditronUserscanHistory->getId()) {
            throw new NoSuchEntityException(__('Coditron_UserscanHistory with id "%1" does not exist.', $coditronUserscanHistoryId));
        }
        return $coditronUserscanHistory;
    }

    /**
     * @inheritDoc
     */
    public function getList(
        \Magento\Framework\Api\SearchCriteriaInterface $criteria
    ) {
        $collection = $this->coditronUserscanHistoryCollectionFactory->create();
        
        $this->collectionProcessor->process($criteria, $collection);
        
        $searchResults = $this->searchResultsFactory->create();
        $searchResults->setSearchCriteria($criteria);
        
        $items = [];
        foreach ($collection as $model) {
            $items[] = $model;
        }
        
        $searchResults->setItems($items);
        $searchResults->setTotalCount($collection->getSize());
        return $searchResults;
    }

    /**
     * @inheritDoc
     */
    public function delete(
        CoditronUserscanHistoryInterface $coditronUserscanHistory
    ) {
        try {
            $coditronUserscanHistoryModel = $this->coditronUserscanHistoryFactory->create();
            $this->resource->load($coditronUserscanHistoryModel, $coditronUserscanHistory->getCoditronUserscanhistoryId());
            $this->resource->delete($coditronUserscanHistoryModel);
        } catch (\Exception $exception) {
            throw new CouldNotDeleteException(__(
                'Could not delete the Coditron_UserscanHistory: %1',
                $exception->getMessage()
            ));
        }
        return true;
    }

    /**
     * @inheritDoc
     */
    public function deleteById($coditronUserscanHistoryId)
    {
        return $this->delete($this->get($coditronUserscanHistoryId));
    }
}

