<?php
declare(strict_types=1);

namespace Coditron\UserHistory\Model\ResourceModel\CoditronUserscanHistory;

use Magento\Framework\Model\ResourceModel\Db\Collection\AbstractCollection;

class Collection extends AbstractCollection
{

    /**
     * @inheritDoc
     */
    protected $_idFieldName = 'id';

    /**
     * @inheritDoc
     */
    protected function _construct()
    {
        $this->_init(
            \Coditron\UserHistory\Model\CoditronUserscanHistory::class,
            \Coditron\UserHistory\Model\ResourceModel\CoditronUserscanHistory::class
        );
    }
}

