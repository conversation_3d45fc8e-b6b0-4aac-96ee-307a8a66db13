<?php
declare(strict_types=1);

namespace Coditron\UserHistory\Model;

use Coditron\UserHistory\Api\Data\CoditronUserscanHistoryInterface;
use Magento\Framework\Model\AbstractModel;

class CoditronUserscanHistory extends AbstractModel implements CoditronUserscanHistoryInterface
{

    /**
     * @inheritDoc
     */
    public function _construct()
    {
        $this->_init(\Coditron\UserHistory\Model\ResourceModel\CoditronUserscanHistory::class);
    }

    /**
     * @inheritDoc
     */
    public function getId()
    {
        return $this->getData(self::ID);
    }

    /**
     * @inheritDoc
     */
    public function setId($id)
    {
        return $this->setData(self::ID, $id);
    }

    /**
     * @inheritDoc
     */
    public function getUserId()
    {
        return $this->getData(self::USER_ID);
    }

    /**
     * @inheritDoc
     */
    public function setUserId($userId)
    {
        return $this->setData(self::USER_ID, $userId);
    }

    /**
     * @inheritDoc
     */
    public function getSellerId()
    {
        return $this->getData(self::SELLER_ID);
    }

    /**
     * @inheritDoc
     */
    public function setSellerId($sellerId)
    {
        return $this->setData(self::SELLER_ID, $sellerId);
    }

    /**
     * @inheritDoc
     */
    public function getCount()
    {
        return $this->getData(self::COUNT);
    }

    /**
     * @inheritDoc
     */
    public function setCount($count)
    {
        return $this->setData(self::COUNT, $count);
    }

    /**
     * @inheritDoc
     */
    public function getScanAt()
    {
        return $this->getData(self::SCAN_AT);
    }

    /**
     * @inheritDoc
     */
    public function setScanAt($scanAt)
    {
        return $this->setData(self::SCAN_AT, $scanAt);
    }
}

