<?php
namespace Co<PERSON>ron\UserHistory\Model;

use Coditron\UserHistory\Api\UserscanhistoryInterface;
use Coditron\UserHistory\Model\CoditronUserscanHistoryFactory;
use Coditron\UserHistory\Model\ResourceModel\CoditronUserscanHistory\CollectionFactory;
use Magento\Framework\Exception\LocalizedException;

class UserscanhistoryRepository implements UserscanhistoryInterface
{
    protected $userscanhistoryFactory;
    protected $collectionFactory;

    public function __construct(
        CoditronUserscanHistoryFactory $userscanhistoryFactory,
        CollectionFactory $collectionFactory
    ) {
        $this->userscanhistoryFactory = $userscanhistoryFactory;
        $this->collectionFactory = $collectionFactory;
    }

    public function updateScanHistory($customerId, $sellerId)
    {
        $collection = $this->collectionFactory->create();
        $item = $collection->addFieldToFilter('user_id', $customerId)
                           ->addFieldToFilter('seller_id', $sellerId)
                           ->getFirstItem();

        if ($item->getId()) {
            // Update existing record
            $newCount = $item->getCount() + 1;
            $item->setCount($newCount);
            $item->setScanAt((new \DateTime())->format('Y-m-d H:i:s'));
        } else {
            // Insert new record
            $item = $this->userscanhistoryFactory->create();
            $item->setUserId($customerId);
            $item->setSellerId($sellerId);
            $item->setCount(1);
            $item->setScanAt((new \DateTime())->format('Y-m-d H:i:s'));
        }

        try {
            $item->save();
        } catch (\Exception $e) {
            throw new LocalizedException(__('Unable to save scan history.'));
        }

        return true;
    }
}
