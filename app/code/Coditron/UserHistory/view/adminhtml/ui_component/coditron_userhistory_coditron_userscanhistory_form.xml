<?xml version="1.0" ?>
<form xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
	<argument name="data" xsi:type="array">
		<item name="js_config" xsi:type="array">
			<item name="provider" xsi:type="string">coditron_userhistory_coditron_userscanhistory_form.coditron_userscanhistory_form_data_source</item>
		</item>
		<item name="label" xsi:type="string" translate="true">General Information</item>
		<item name="template" xsi:type="string">templates/form/collapsible</item>
	</argument>
	<settings>
		<buttons>
			<button name="back" class="Coditron\UserHistory\Block\Adminhtml\Coditron\UserscanHistory\Edit\BackButton"/>
			<button name="delete" class="Coditron\UserHistory\Block\Adminhtml\Coditron\UserscanHistory\Edit\DeleteButton"/>
			<button name="save" class="Coditron\UserHistory\Block\Adminhtml\Coditron\UserscanHistory\Edit\SaveButton"/>
			<button name="save_and_continue" class="Coditron\UserHistory\Block\Adminhtml\Coditron\UserscanHistory\Edit\SaveAndContinueButton"/>
		</buttons>
		<namespace>coditron_userhistory_coditron_userscanhistory_form</namespace>
		<dataScope>data</dataScope>
		<deps>
			<dep>coditron_userhistory_coditron_userscanhistory_form.coditron_userscanhistory_form_data_source</dep>
		</deps>
	</settings>
	<dataSource name="coditron_userscanhistory_form_data_source">
		<argument name="data" xsi:type="array">
			<item name="js_config" xsi:type="array">
				<item name="component" xsi:type="string">Magento_Ui/js/form/provider</item>
			</item>
		</argument>
		<settings>
			<submitUrl path="*/*/save"/>
		</settings>
		<dataProvider name="coditron_userscanhistory_form_data_source" class="Coditron\UserHistory\Model\CoditronUserscanHistory\DataProvider">
			<settings>
				<requestFieldName>id</requestFieldName>
				<primaryFieldName>id</primaryFieldName>
			</settings>
		</dataProvider>
	</dataSource>
	<fieldset name="general">
		<settings>
			<label>General</label>
		</settings>
<!-- 		<field name="id" formElement="input" sortOrder="10">
			<argument name="data" xsi:type="array">
				<item name="config" xsi:type="array">
					<item name="source" xsi:type="string">Coditron_UserscanHistory</item>
				</item>
			</argument>
			<settings>
				<dataType>text</dataType>
				<label translate="true">Id</label>
				<dataScope>id</dataScope>
				<validation>
					<rule name="required-entry" xsi:type="boolean">false</rule>
				</validation>
			</settings>
		</field> -->
		<field name="user_id" formElement="input" sortOrder="20">
			<argument name="data" xsi:type="array">
				<item name="config" xsi:type="array">
					<item name="source" xsi:type="string">Coditron_UserscanHistory</item>
				</item>
			</argument>
			<settings>
				<dataType>text</dataType>
				<label translate="true">User Id</label>
				<dataScope>user_id</dataScope>
				<validation>
					<rule name="required-entry" xsi:type="boolean">false</rule>
				</validation>
			</settings>
		</field>
		<field name="seller_id" formElement="textarea" sortOrder="30">
			<argument name="data" xsi:type="array">
				<item name="config" xsi:type="array">
					<item name="source" xsi:type="string">Coditron_UserscanHistory</item>
				</item>
			</argument>
			<settings>
				<dataType>text</dataType>
				<label translate="true">Seller Id</label>
				<dataScope>seller_id</dataScope>
				<validation>
					<rule name="required-entry" xsi:type="boolean">false</rule>
				</validation>
			</settings>
		</field>
		<field name="count" formElement="input" sortOrder="40">
			<argument name="data" xsi:type="array">
				<item name="config" xsi:type="array">
					<item name="source" xsi:type="string">Coditron_UserscanHistory</item>
				</item>
			</argument>
			<settings>
				<dataType>text</dataType>
				<label translate="true">Count</label>
				<dataScope>count</dataScope>
				<validation>
					<rule name="required-entry" xsi:type="boolean">false</rule>
				</validation>
			</settings>
		</field>
		<field name="scan_at" formElement="date" sortOrder="50">
			<argument name="data" xsi:type="array">
				<item name="config" xsi:type="array">
					<item name="source" xsi:type="string">Coditron_UserscanHistory</item>
				</item>
			</argument>
			<settings>
				<dataType>text</dataType>
				<label translate="true">Scan At</label>
				<dataScope>scan_at</dataScope>
				<validation>
					<rule name="required-entry" xsi:type="boolean">false</rule>
				</validation>
			</settings>
		</field>
	</fieldset>
</form>
