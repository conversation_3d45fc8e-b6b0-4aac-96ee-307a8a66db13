<?php
declare(strict_types=1);

namespace Coditron\UserHistory\Api;

use Magento\Framework\Api\SearchCriteriaInterface;

interface CoditronUserscanHistoryRepositoryInterface
{

    /**
     * Save Coditron_UserscanHistory
     * @param \Coditron\UserHistory\Api\Data\CoditronUserscanHistoryInterface $coditronUserscanHistory
     * @return \Coditron\UserHistory\Api\Data\CoditronUserscanHistoryInterface
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function save(
        \Coditron\UserHistory\Api\Data\CoditronUserscanHistoryInterface $coditronUserscanHistory
    );

    /**
     * Retrieve Coditron_UserscanHistory
     * @param string $coditronUserscanhistoryId
     * @return \Coditron\UserHistory\Api\Data\CoditronUserscanHistoryInterface
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function get($coditronUserscanhistoryId);

    /**
     * Retrieve Coditron_UserscanHistory matching the specified criteria.
     * @param \Magento\Framework\Api\SearchCriteriaInterface $searchCriteria
     * @return \Coditron\UserHistory\Api\Data\CoditronUserscanHistorySearchResultsInterface
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function getList(
        \Magento\Framework\Api\SearchCriteriaInterface $searchCriteria
    );

    /**
     * Delete Coditron_UserscanHistory
     * @param \Coditron\UserHistory\Api\Data\CoditronUserscanHistoryInterface $coditronUserscanHistory
     * @return bool true on success
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function delete(
        \Coditron\UserHistory\Api\Data\CoditronUserscanHistoryInterface $coditronUserscanHistory
    );

    /**
     * Delete Coditron_UserscanHistory by ID
     * @param string $coditronUserscanhistoryId
     * @return bool true on success
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function deleteById($coditronUserscanhistoryId);
}

