<?php
declare(strict_types=1);

namespace Coditron\UserHistory\Api\Data;

interface CoditronUserscanHistorySearchResultsInterface extends \Magento\Framework\Api\SearchResultsInterface
{

    /**
     * Get Coditron_UserscanHistory list.
     * @return \Coditron\UserHistory\Api\Data\CoditronUserscanHistoryInterface[]
     */
    public function getItems();

    /**
     * Set id list.
     * @param \Coditron\UserHistory\Api\Data\CoditronUserscanHistoryInterface[] $items
     * @return $this
     */
    public function setItems(array $items);
}

