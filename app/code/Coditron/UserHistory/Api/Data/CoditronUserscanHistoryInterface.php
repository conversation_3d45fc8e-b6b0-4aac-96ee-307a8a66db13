<?php
declare(strict_types=1);

namespace Coditron\UserHistory\Api\Data;

interface CoditronUserscanHistoryInterface
{

    const SCAN_AT = 'scan_at';
    const USER_ID = 'user_id';
    const SELLER_ID = 'seller_id';
    const ID = 'id';
    const COUNT = 'count';


    /**
     * Get id
     * @return string|null
     */
    public function getId();

    /**
     * Set id
     * @param string $id
     * @return \Coditron\UserHistory\CoditronUserscanHistory\Api\Data\CoditronUserscanHistoryInterface
     */
    public function setId($id);

    /**
     * Get user_id
     * @return string|null
     */
    public function getUserId();

    /**
     * Set user_id
     * @param string $userId
     * @return \Coditron\UserHistory\CoditronUserscanHistory\Api\Data\CoditronUserscanHistoryInterface
     */
    public function setUserId($userId);

    /**
     * Get seller_id
     * @return string|null
     */
    public function getSellerId();

    /**
     * Set seller_id
     * @param string $sellerId
     * @return \Coditron\UserHistory\CoditronUserscanHistory\Api\Data\CoditronUserscanHistoryInterface
     */
    public function setSellerId($sellerId);

    /**
     * Get count
     * @return string|null
     */
    public function getCount();

    /**
     * Set count
     * @param string $count
     * @return \Coditron\UserHistory\CoditronUserscanHistory\Api\Data\CoditronUserscanHistoryInterface
     */
    public function setCount($count);

    /**
     * Get scan_at
     * @return string|null
     */
    public function getScanAt();

    /**
     * Set scan_at
     * @param string $scanAt
     * @return \Coditron\UserHistory\CoditronUserscanHistory\Api\Data\CoditronUserscanHistoryInterface
     */
    public function setScanAt($scanAt);
}

