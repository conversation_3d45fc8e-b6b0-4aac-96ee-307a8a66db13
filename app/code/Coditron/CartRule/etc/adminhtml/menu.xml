<?xml version="1.0" ?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Backend:etc/menu.xsd">
	<menu>
		<add id="Coditron_CartRule::coupon" title="Seller Coupons" module="Coditron_CartRule" sortOrder="120" resource="Coditron_CartRule::coupon" parent="Coditron::top_level"/>

		<add id="Coditron_CartRule::coditron_cartrule_comaveseller_coupon" title="Manage Coupons" module="Coditron_CartRule" sortOrder="130" resource="Magento_Backend::content" parent="Coditron_CartRule::coupon" action="coditron_cartrule/comavesellercoupon/index"/>
	</menu>
</config>
