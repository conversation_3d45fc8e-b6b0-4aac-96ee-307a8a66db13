<?php

namespace Coditron\CustomShippingRate\Controller\ShipTableRates;

class NewAction extends \Coditron\CustomShippingRate\Controller\AbstractShiprate
{
    /**
     * Create New Seller Shipping Rate action
     * Forwards to edit action for both regular shipping methods and thresholds
     */
    public function execute()
    {
        $resultForward = $this->resultForwardFactory->create();
        $resultForward->forward('edit');
        return $resultForward;
    }
}
