<?php
/**
 * Data provider for Free Shipping Threshold listing
 */
declare(strict_types=1);

namespace Coditron\CustomShippingRate\Ui\DataProvider;

use Coditron\CustomShippingRate\Model\ResourceModel\ShipTableRates\CollectionFactory;
use Magento\Framework\Api\Filter;
use Magento\Ui\DataProvider\AbstractDataProvider;
use Coditron\CustomShippingRate\Helper\Data as CustomShippingHelper;

class ThresholdListDataProvider extends AbstractDataProvider
{
    /**
     * @var CustomShippingHelper
     */
    protected $helper;

    /**
     * @var array
     */
    protected $loadedData;

    /**
     * @param string $name
     * @param string $primaryFieldName
     * @param string $requestFieldName
     * @param CollectionFactory $collectionFactory
     * @param CustomShippingHelper $helper
     * @param array $meta
     * @param array $data
     */
    public function __construct(
        $name,
        $primaryFieldName,
        $requestFieldName,
        CollectionFactory $collectionFactory,
        CustomShippingHelper $helper,
        array $meta = [],
        array $data = []
    ) {
        $this->collection = $collectionFactory->create();
        $this->helper = $helper;
        parent::__construct($name, $primaryFieldName, $requestFieldName, $meta, $data);
    }

    /**
     * Get data
     *
     * @return array
     */
    public function getData()
    {
        if (isset($this->loadedData)) {
            return $this->loadedData;
        }

        $sellerId = $this->helper->getSellerId();

        // Filter collection to show only records with min_order_amount > 0 for current seller
        $this->collection->addFieldToFilter('seller_id', $sellerId)
                        ->addFieldToFilter('min_order_amount', ['gt' => 0]);

        $this->loadedData = $this->collection->toArray();
        return $this->loadedData;
    }

    /**
     * Add filter
     *
     * @param Filter $filter
     * @return void
     */
    public function addFilter(Filter $filter)
    {
        if ($filter->getField() !== 'fulltext') {
            $this->collection->addFieldToFilter(
                $filter->getField(),
                [$filter->getConditionType() => $filter->getValue()]
            );
        } else {
            $value = trim($filter->getValue());
            $this->collection->addFieldToFilter(
                [
                    ['attribute' => 'countries'],
                    ['attribute' => 'min_order_amount']
                ],
                [
                    ['like' => "%{$value}%"],
                    ['like' => "%{$value}%"]
                ]
            );
        }
    }
}
