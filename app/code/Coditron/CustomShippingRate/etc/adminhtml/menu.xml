<?xml version="1.0" ?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Backend:etc/menu.xsd">
	<menu>
		<add id="Coditron_CustomShippingRate::tablerates" title="Seller Shipping Rates" module="Coditron_CustomShippingRate" sortOrder="100" resource="Coditron_CustomShippingRate::tablerates" parent="Coditron::top_level"/>

		<add id="Coditron_CustomShippingRate::coditron_customshippingrate_shiptablerates" title="Table Rates" module="Coditron_CustomShippingRate" sortOrder="110" resource="Magento_Backend::content" parent="Coditron_CustomShippingRate::tablerates" action="coditron_customshippingrate/shiptablerates/index"/>
	</menu>
</config>
